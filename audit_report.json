{"scan_timestamp": "2025-06-08T17:05:28.465260", "root_directory": "E:\\Improving GPAce\\Creating an App", "total_files": 161, "summary_by_extension": {".html": {"count": 18, "total_size_bytes": 536919, "total_size_kb": 524.33, "average_size_bytes": 29828.83, "largest_file": {"path": "grind.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\grind.html", "size_bytes": 201061, "size_kb": 196.35, "extension": ".html", "directory": ".", "filename": "grind.html", "modified_time": "2025-05-05T17:13:28.879082"}, "smallest_file": {"path": "404.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\404.html", "size_bytes": 2114, "size_kb": 2.06, "extension": ".html", "directory": ".", "filename": "404.html", "modified_time": "2025-05-04T02:05:19.762550"}}, ".css": {"count": 29, "total_size_bytes": 491856, "total_size_kb": 480.33, "average_size_bytes": 16960.55, "largest_file": {"path": "grind.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\grind.css", "size_bytes": 125643, "size_kb": 122.7, "extension": ".css", "directory": ".", "filename": "grind.css", "modified_time": "2025-04-23T17:54:24.477034"}, "smallest_file": {"path": "styles\\index.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\styles\\index.css", "size_bytes": 414, "size_kb": 0.4, "extension": ".css", "directory": "styles", "filename": "index.css", "modified_time": "2025-04-23T18:03:05.856265"}}, ".js": {"count": 114, "total_size_bytes": 1614180, "total_size_kb": 1576.35, "average_size_bytes": 14159.47, "largest_file": {"path": "js\\ai-researcher.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\ai-researcher.js", "size_bytes": 101375, "size_kb": 99.0, "extension": ".js", "directory": "js", "filename": "ai-researcher.js", "modified_time": "2025-04-28T04:23:25.695364"}, "smallest_file": {"path": "js\\data-loader.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\data-loader.js", "size_bytes": 0, "size_kb": 0.0, "extension": ".js", "directory": "js", "filename": "data-loader.js", "modified_time": "2025-04-23T18:02:06.683452"}}}, "files": [{"path": "css\\academic-details.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\academic-details.css", "size_bytes": 27044, "size_kb": 26.41, "extension": ".css", "directory": "css", "filename": "academic-details.css", "modified_time": "2025-04-26T09:00:15.678163"}, {"path": "css\\ai-search-response.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\ai-search-response.css", "size_bytes": 12338, "size_kb": 12.05, "extension": ".css", "directory": "css", "filename": "ai-search-response.css", "modified_time": "2025-04-23T17:54:30.156056"}, {"path": "css\\alarm-service.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\alarm-service.css", "size_bytes": 11193, "size_kb": 10.93, "extension": ".css", "directory": "css", "filename": "alarm-service.css", "modified_time": "2025-04-23T17:54:30.188086"}, {"path": "css\\compact-style.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\compact-style.css", "size_bytes": 4220, "size_kb": 4.12, "extension": ".css", "directory": "css", "filename": "compact-style.css", "modified_time": "2025-04-23T17:54:30.223117"}, {"path": "css\\daily-calendar.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\daily-calendar.css", "size_bytes": 50181, "size_kb": 49.0, "extension": ".css", "directory": "css", "filename": "daily-calendar.css", "modified_time": "2025-04-23T17:54:30.257149"}, {"path": "css\\extracted.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\extracted.css", "size_bytes": 39627, "size_kb": 38.7, "extension": ".css", "directory": "css", "filename": "extracted.css", "modified_time": "2025-04-23T17:54:30.288177"}, {"path": "css\\flashcards.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\flashcards.css", "size_bytes": 5129, "size_kb": 5.01, "extension": ".css", "directory": "css", "filename": "flashcards.css", "modified_time": "2025-04-23T17:54:30.323210"}, {"path": "css\\notification.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\notification.css", "size_bytes": 967, "size_kb": 0.94, "extension": ".css", "directory": "css", "filename": "notification.css", "modified_time": "2025-04-23T17:54:30.386267"}, {"path": "css\\priority-calculator.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\priority-calculator.css", "size_bytes": 7623, "size_kb": 7.44, "extension": ".css", "directory": "css", "filename": "priority-calculator.css", "modified_time": "2025-04-26T13:49:45.979213"}, {"path": "css\\priority-list.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\priority-list.css", "size_bytes": 10003, "size_kb": 9.77, "extension": ".css", "directory": "css", "filename": "priority-list.css", "modified_time": "2025-04-26T14:02:02.331014"}, {"path": "css\\settings.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\settings.css", "size_bytes": 7412, "size_kb": 7.24, "extension": ".css", "directory": "css", "filename": "settings.css", "modified_time": "2025-05-01T10:39:38.629989"}, {"path": "css\\sideDrawer.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\sideDrawer.css", "size_bytes": 15934, "size_kb": 15.56, "extension": ".css", "directory": "css", "filename": "sideDrawer.css", "modified_time": "2025-04-23T17:54:30.419297"}, {"path": "css\\simulation-enhancer.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\simulation-enhancer.css", "size_bytes": 4281, "size_kb": 4.18, "extension": ".css", "directory": "css", "filename": "simulation-enhancer.css", "modified_time": "2025-04-23T17:54:30.452327"}, {"path": "css\\sleep-saboteurs.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\sleep-saboteurs.css", "size_bytes": 17245, "size_kb": 16.84, "extension": ".css", "directory": "css", "filename": "sleep-saboteurs.css", "modified_time": "2025-05-01T12:53:59.203578"}, {"path": "css\\study-spaces.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\study-spaces.css", "size_bytes": 19318, "size_kb": 18.87, "extension": ".css", "directory": "css", "filename": "study-spaces.css", "modified_time": "2025-04-26T15:48:43.292228"}, {"path": "css\\subject-marks.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\subject-marks.css", "size_bytes": 4989, "size_kb": 4.87, "extension": ".css", "directory": "css", "filename": "subject-marks.css", "modified_time": "2025-04-26T14:36:23.189570"}, {"path": "css\\task-display.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\task-display.css", "size_bytes": 828, "size_kb": 0.81, "extension": ".css", "directory": "css", "filename": "task-display.css", "modified_time": "2025-04-23T17:54:30.544412"}, {"path": "css\\task-notes.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\task-notes.css", "size_bytes": 9506, "size_kb": 9.28, "extension": ".css", "directory": "css", "filename": "task-notes.css", "modified_time": "2025-05-04T17:31:24.747914"}, {"path": "css\\taskLinks.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\taskLinks.css", "size_bytes": 9748, "size_kb": 9.52, "extension": ".css", "directory": "css", "filename": "taskLinks.css", "modified_time": "2025-05-05T17:15:27.929005"}, {"path": "css\\test-feedback.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\test-feedback.css", "size_bytes": 16264, "size_kb": 15.88, "extension": ".css", "directory": "css", "filename": "test-feedback.css", "modified_time": "2025-04-23T17:54:30.622483"}, {"path": "css\\text-expansion.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\text-expansion.css", "size_bytes": 6288, "size_kb": 6.14, "extension": ".css", "directory": "css", "filename": "text-expansion.css", "modified_time": "2025-04-23T17:54:30.659518"}, {"path": "css\\workspace.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\css\\workspace.css", "size_bytes": 40046, "size_kb": 39.11, "extension": ".css", "directory": "css", "filename": "workspace.css", "modified_time": "2025-05-04T18:30:43.473631"}, {"path": "grind.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\grind.css", "size_bytes": 125643, "size_kb": 122.7, "extension": ".css", "directory": ".", "filename": "grind.css", "modified_time": "2025-04-23T17:54:24.477034"}, {"path": "relaxed-mode\\style.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\relaxed-mode\\style.css", "size_bytes": 10525, "size_kb": 10.28, "extension": ".css", "directory": "relaxed-mode", "filename": "style.css", "modified_time": "2025-04-23T18:03:05.487651"}, {"path": "styles\\calendar.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\styles\\calendar.css", "size_bytes": 9141, "size_kb": 8.93, "extension": ".css", "directory": "styles", "filename": "calendar.css", "modified_time": "2025-04-23T18:03:05.822082"}, {"path": "styles\\index.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\styles\\index.css", "size_bytes": 414, "size_kb": 0.4, "extension": ".css", "directory": "styles", "filename": "index.css", "modified_time": "2025-04-23T18:03:05.856265"}, {"path": "styles\\main.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\styles\\main.css", "size_bytes": 15630, "size_kb": 15.26, "extension": ".css", "directory": "styles", "filename": "main.css", "modified_time": "2025-04-23T18:03:05.893550"}, {"path": "styles\\study-spaces.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\styles\\study-spaces.css", "size_bytes": 3850, "size_kb": 3.76, "extension": ".css", "directory": "styles", "filename": "study-spaces.css", "modified_time": "2025-04-23T18:03:05.927140"}, {"path": "styles\\tasks.css", "absolute_path": "E:\\Improving GPAce\\Creating an App\\styles\\tasks.css", "size_bytes": 6469, "size_kb": 6.32, "extension": ".css", "directory": "styles", "filename": "tasks.css", "modified_time": "2025-04-23T18:03:05.963363"}, {"path": "404.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\404.html", "size_bytes": 2114, "size_kb": 2.06, "extension": ".html", "directory": ".", "filename": "404.html", "modified_time": "2025-05-04T02:05:19.762550"}, {"path": "academic-details.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\academic-details.html", "size_bytes": 26146, "size_kb": 25.53, "extension": ".html", "directory": ".", "filename": "academic-details.html", "modified_time": "2025-05-01T13:16:45.665786"}, {"path": "daily-calendar.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\daily-calendar.html", "size_bytes": 10240, "size_kb": 10.0, "extension": ".html", "directory": ".", "filename": "daily-calendar.html", "modified_time": "2025-05-01T13:11:42.592993"}, {"path": "extracted.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\extracted.html", "size_bytes": 92348, "size_kb": 90.18, "extension": ".html", "directory": ".", "filename": "extracted.html", "modified_time": "2025-05-01T13:06:46.761699"}, {"path": "flashcards.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\flashcards.html", "size_bytes": 16728, "size_kb": 16.34, "extension": ".html", "directory": ".", "filename": "flashcards.html", "modified_time": "2025-05-04T02:02:26.722164"}, {"path": "grind.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\grind.html", "size_bytes": 201061, "size_kb": 196.35, "extension": ".html", "directory": ".", "filename": "grind.html", "modified_time": "2025-05-05T17:13:28.879082"}, {"path": "index.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\index.html", "size_bytes": 2464, "size_kb": 2.41, "extension": ".html", "directory": ".", "filename": "index.html", "modified_time": "2025-05-04T02:04:06.771702"}, {"path": "instant-test-feedback.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\instant-test-feedback.html", "size_bytes": 23222, "size_kb": 22.68, "extension": ".html", "directory": ".", "filename": "instant-test-feedback.html", "modified_time": "2025-05-04T02:05:05.720021"}, {"path": "landing.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\landing.html", "size_bytes": 55202, "size_kb": 53.91, "extension": ".html", "directory": ".", "filename": "landing.html", "modified_time": "2025-05-04T02:04:26.515637"}, {"path": "priority-calculator.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\priority-calculator.html", "size_bytes": 7020, "size_kb": 6.86, "extension": ".html", "directory": ".", "filename": "priority-calculator.html", "modified_time": "2025-05-04T09:36:00.129172"}, {"path": "priority-list.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\priority-list.html", "size_bytes": 5227, "size_kb": 5.1, "extension": ".html", "directory": ".", "filename": "priority-list.html", "modified_time": "2025-05-04T09:36:18.270604"}, {"path": "relaxed-mode\\index.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\relaxed-mode\\index.html", "size_bytes": 7111, "size_kb": 6.94, "extension": ".html", "directory": "relaxed-mode", "filename": "index.html", "modified_time": "2025-05-04T09:36:57.537756"}, {"path": "settings.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\settings.html", "size_bytes": 5577, "size_kb": 5.45, "extension": ".html", "directory": ".", "filename": "settings.html", "modified_time": "2025-05-01T13:06:46.766703"}, {"path": "sleep-saboteurs.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\sleep-saboteurs.html", "size_bytes": 7732, "size_kb": 7.55, "extension": ".html", "directory": ".", "filename": "sleep-saboteurs.html", "modified_time": "2025-05-04T09:36:36.227582"}, {"path": "study-spaces.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\study-spaces.html", "size_bytes": 21065, "size_kb": 20.57, "extension": ".html", "directory": ".", "filename": "study-spaces.html", "modified_time": "2025-05-01T13:20:15.623512"}, {"path": "subject-marks.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\subject-marks.html", "size_bytes": 8598, "size_kb": 8.4, "extension": ".html", "directory": ".", "filename": "subject-marks.html", "modified_time": "2025-05-04T02:02:59.411448"}, {"path": "tasks.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\tasks.html", "size_bytes": 24458, "size_kb": 23.88, "extension": ".html", "directory": ".", "filename": "tasks.html", "modified_time": "2025-05-04T02:03:23.837308"}, {"path": "workspace.html", "absolute_path": "E:\\Improving GPAce\\Creating an App\\workspace.html", "size_bytes": 20606, "size_kb": 20.12, "extension": ".html", "directory": ".", "filename": "workspace.html", "modified_time": "2025-05-04T18:30:43.473631"}, {"path": "js\\academic-details.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\academic-details.js", "size_bytes": 2090, "size_kb": 2.04, "extension": ".js", "directory": "js", "filename": "academic-details.js", "modified_time": "2025-04-26T09:36:15.494982"}, {"path": "js\\add-favicon.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\add-favicon.js", "size_bytes": 2888, "size_kb": 2.82, "extension": ".js", "directory": "js", "filename": "add-favicon.js", "modified_time": "2025-04-23T18:02:05.878582"}, {"path": "js\\ai-latex-conversion.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\ai-latex-conversion.js", "size_bytes": 746, "size_kb": 0.73, "extension": ".js", "directory": "js", "filename": "ai-latex-conversion.js", "modified_time": "2025-04-23T18:02:05.919945"}, {"path": "js\\ai-researcher.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\ai-researcher.js", "size_bytes": 101375, "size_kb": 99.0, "extension": ".js", "directory": "js", "filename": "ai-researcher.js", "modified_time": "2025-04-28T04:23:25.695364"}, {"path": "js\\alarm-data-service.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\alarm-data-service.js", "size_bytes": 9780, "size_kb": 9.55, "extension": ".js", "directory": "js", "filename": "alarm-data-service.js", "modified_time": "2025-04-23T18:02:06.022795"}, {"path": "js\\alarm-handler.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\alarm-handler.js", "size_bytes": 2032, "size_kb": 1.98, "extension": ".js", "directory": "js", "filename": "alarm-handler.js", "modified_time": "2025-04-23T18:02:06.065116"}, {"path": "js\\alarm-mini-display.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\alarm-mini-display.js", "size_bytes": 3702, "size_kb": 3.62, "extension": ".js", "directory": "js", "filename": "alarm-mini-display.js", "modified_time": "2025-04-23T18:02:06.109735"}, {"path": "js\\alarm-service-worker.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\alarm-service-worker.js", "size_bytes": 4761, "size_kb": 4.65, "extension": ".js", "directory": "js", "filename": "alarm-service-worker.js", "modified_time": "2025-04-23T18:02:06.156026"}, {"path": "js\\alarm-service.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\alarm-service.js", "size_bytes": 20505, "size_kb": 20.02, "extension": ".js", "directory": "js", "filename": "alarm-service.js", "modified_time": "2025-05-01T12:39:04.871886"}, {"path": "js\\api-optimization.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\api-optimization.js", "size_bytes": 17304, "size_kb": 16.9, "extension": ".js", "directory": "js", "filename": "api-optimization.js", "modified_time": "2025-04-23T18:02:06.261485"}, {"path": "js\\api-settings.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\api-settings.js", "size_bytes": 6746, "size_kb": 6.59, "extension": ".js", "directory": "js", "filename": "api-settings.js", "modified_time": "2025-04-27T16:28:51.354573"}, {"path": "js\\apiSettingsManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\apiSettingsManager.js", "size_bytes": 5021, "size_kb": 4.9, "extension": ".js", "directory": "js", "filename": "apiSettingsManager.js", "modified_time": "2025-04-26T15:42:02.915031"}, {"path": "js\\auth.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\auth.js", "size_bytes": 11414, "size_kb": 11.15, "extension": ".js", "directory": "js", "filename": "auth.js", "modified_time": "2025-04-23T18:02:06.361555"}, {"path": "js\\calendar-views.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\calendar-views.js", "size_bytes": 33294, "size_kb": 32.51, "extension": ".js", "directory": "js", "filename": "calendar-views.js", "modified_time": "2025-04-26T15:19:47.480445"}, {"path": "js\\calendarManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\calendarManager.js", "size_bytes": 43345, "size_kb": 42.33, "extension": ".js", "directory": "js", "filename": "calendarManager.js", "modified_time": "2025-04-26T15:19:29.028142"}, {"path": "js\\clock-display.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\clock-display.js", "size_bytes": 2038, "size_kb": 1.99, "extension": ".js", "directory": "js", "filename": "clock-display.js", "modified_time": "2025-05-01T12:38:35.766161"}, {"path": "js\\common-header.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\common-header.js", "size_bytes": 5780, "size_kb": 5.64, "extension": ".js", "directory": "js", "filename": "common-header.js", "modified_time": "2025-04-23T18:02:06.519770"}, {"path": "js\\common.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\common.js", "size_bytes": 1758, "size_kb": 1.72, "extension": ".js", "directory": "js", "filename": "common.js", "modified_time": "2025-04-23T18:02:06.564831"}, {"path": "js\\cross-tab-sync.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\cross-tab-sync.js", "size_bytes": 10833, "size_kb": 10.58, "extension": ".js", "directory": "js", "filename": "cross-tab-sync.js", "modified_time": "2025-05-05T17:15:56.135142"}, {"path": "js\\currentTaskManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\currentTaskManager.js", "size_bytes": 11858, "size_kb": 11.58, "extension": ".js", "directory": "js", "filename": "currentTaskManager.js", "modified_time": "2025-04-27T06:17:27.054250"}, {"path": "js\\data-loader.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\data-loader.js", "size_bytes": 0, "size_kb": 0.0, "extension": ".js", "directory": "js", "filename": "data-loader.js", "modified_time": "2025-04-23T18:02:06.683452"}, {"path": "js\\data-sync-integration.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\data-sync-integration.js", "size_bytes": 2027, "size_kb": 1.98, "extension": ".js", "directory": "js", "filename": "data-sync-integration.js", "modified_time": "2025-04-26T14:33:29.717976"}, {"path": "js\\data-sync-manager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\data-sync-manager.js", "size_bytes": 6643, "size_kb": 6.49, "extension": ".js", "directory": "js", "filename": "data-sync-manager.js", "modified_time": "2025-04-23T18:02:06.727932"}, {"path": "js\\energyHologram.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\energyHologram.js", "size_bytes": 8670, "size_kb": 8.47, "extension": ".js", "directory": "js", "filename": "energyHologram.js", "modified_time": "2025-04-23T18:02:06.772807"}, {"path": "js\\energyLevels.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\energyLevels.js", "size_bytes": 1616, "size_kb": 1.58, "extension": ".js", "directory": "js", "filename": "energyLevels.js", "modified_time": "2025-04-23T18:02:06.815735"}, {"path": "js\\fileViewer.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\fileViewer.js", "size_bytes": 17164, "size_kb": 16.76, "extension": ".js", "directory": "js", "filename": "fileViewer.js", "modified_time": "2025-04-23T18:02:06.862710"}, {"path": "js\\firebase-config.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\firebase-config.js", "size_bytes": 3200, "size_kb": 3.12, "extension": ".js", "directory": "js", "filename": "firebase-config.js", "modified_time": "2025-04-26T15:12:24.183990"}, {"path": "js\\firebase-init.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\firebase-init.js", "size_bytes": 3040, "size_kb": 2.97, "extension": ".js", "directory": "js", "filename": "firebase-init.js", "modified_time": "2025-04-26T14:32:55.737745"}, {"path": "js\\firebaseAuth.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\firebaseAuth.js", "size_bytes": 2385, "size_kb": 2.33, "extension": ".js", "directory": "js", "filename": "firebaseAuth.js", "modified_time": "2025-04-26T15:43:48.282243"}, {"path": "js\\firebaseConfig.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\firebaseConfig.js", "size_bytes": 1398, "size_kb": 1.37, "extension": ".js", "directory": "js", "filename": "firebaseConfig.js", "modified_time": "2025-04-23T18:02:06.906677"}, {"path": "js\\firestore-global.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\firestore-global.js", "size_bytes": 1454, "size_kb": 1.42, "extension": ".js", "directory": "js", "filename": "firestore-global.js", "modified_time": "2025-04-26T14:36:33.875000"}, {"path": "js\\firestore.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\firestore.js", "size_bytes": 21209, "size_kb": 20.71, "extension": ".js", "directory": "js", "filename": "firestore.js", "modified_time": "2025-04-23T18:02:06.952492"}, {"path": "js\\flashcardManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\flashcardManager.js", "size_bytes": 49955, "size_kb": 48.78, "extension": ".js", "directory": "js", "filename": "flashcardManager.js", "modified_time": "2025-04-23T18:02:07.001345"}, {"path": "js\\flashcardTaskIntegration.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\flashcardTaskIntegration.js", "size_bytes": 27730, "size_kb": 27.08, "extension": ".js", "directory": "js", "filename": "flashcardTaskIntegration.js", "modified_time": "2025-04-23T18:02:07.097757"}, {"path": "js\\flashcards.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\flashcards.js", "size_bytes": 51674, "size_kb": 50.46, "extension": ".js", "directory": "js", "filename": "flashcards.js", "modified_time": "2025-04-23T18:02:07.052186"}, {"path": "js\\gemini-api.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\gemini-api.js", "size_bytes": 4024, "size_kb": 3.93, "extension": ".js", "directory": "js", "filename": "gemini-api.js", "modified_time": "2025-04-23T18:02:07.145030"}, {"path": "js\\googleDriveApi.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\googleDriveApi.js", "size_bytes": 72304, "size_kb": 70.61, "extension": ".js", "directory": "js", "filename": "googleDriveApi.js", "modified_time": "2025-04-30T17:42:04.647339"}, {"path": "js\\googleGenerativeAI.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\googleGenerativeAI.js", "size_bytes": 506, "size_kb": 0.49, "extension": ".js", "directory": "js", "filename": "googleGenerativeAI.js", "modified_time": "2025-04-23T18:02:07.248054"}, {"path": "js\\grind-speech-synthesis.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\grind-speech-synthesis.js", "size_bytes": 34139, "size_kb": 33.34, "extension": ".js", "directory": "js", "filename": "grind-speech-synthesis.js", "modified_time": "2025-04-23T18:02:07.295346"}, {"path": "js\\imageAnalyzer.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\imageAnalyzer.js", "size_bytes": 8580, "size_kb": 8.38, "extension": ".js", "directory": "js", "filename": "imageAnalyzer.js", "modified_time": "2025-04-26T15:12:24.246046"}, {"path": "js\\indexedDB.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\indexedDB.js", "size_bytes": 0, "size_kb": 0.0, "extension": ".js", "directory": "js", "filename": "indexedDB.js", "modified_time": "2025-04-23T18:02:07.366586"}, {"path": "js\\initFirestoreData.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\initFirestoreData.js", "size_bytes": 11030, "size_kb": 10.77, "extension": ".js", "directory": "js", "filename": "initFirestoreData.js", "modified_time": "2025-04-23T18:02:07.411987"}, {"path": "js\\inject-header.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\inject-header.js", "size_bytes": 2290, "size_kb": 2.24, "extension": ".js", "directory": "js", "filename": "inject-header.js", "modified_time": "2025-04-23T18:02:07.457022"}, {"path": "js\\markdown-converter.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\markdown-converter.js", "size_bytes": 11518, "size_kb": 11.25, "extension": ".js", "directory": "js", "filename": "markdown-converter.js", "modified_time": "2025-04-23T18:02:07.507104"}, {"path": "js\\marks-tracking.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\marks-tracking.js", "size_bytes": 0, "size_kb": 0.0, "extension": ".js", "directory": "js", "filename": "marks-tracking.js", "modified_time": "2025-04-23T18:02:07.533613"}, {"path": "js\\pandoc-fallback.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\pandoc-fallback.js", "size_bytes": 2505, "size_kb": 2.45, "extension": ".js", "directory": "js", "filename": "pandoc-fallback.js", "modified_time": "2025-04-23T18:02:07.582658"}, {"path": "js\\pomodoroGlobal.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\pomodoroGlobal.js", "size_bytes": 9682, "size_kb": 9.46, "extension": ".js", "directory": "js", "filename": "pomodoroGlobal.js", "modified_time": "2025-04-23T18:02:07.633054"}, {"path": "js\\pomodoroTimer.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\pomodoroTimer.js", "size_bytes": 34711, "size_kb": 33.9, "extension": ".js", "directory": "js", "filename": "pomodoroTimer.js", "modified_time": "2025-04-23T18:02:07.681926"}, {"path": "js\\priority-list-sorting.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\priority-list-sorting.js", "size_bytes": 10551, "size_kb": 10.3, "extension": ".js", "directory": "js", "filename": "priority-list-sorting.js", "modified_time": "2025-04-23T18:02:07.725835"}, {"path": "js\\priority-list-utils.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\priority-list-utils.js", "size_bytes": 26792, "size_kb": 26.16, "extension": ".js", "directory": "js", "filename": "priority-list-utils.js", "modified_time": "2025-05-04T18:16:01.434957"}, {"path": "js\\priority-sync-fix.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\priority-sync-fix.js", "size_bytes": 4836, "size_kb": 4.72, "extension": ".js", "directory": "js", "filename": "priority-sync-fix.js", "modified_time": "2025-04-23T18:02:07.771272"}, {"path": "js\\priority-worker-wrapper.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\priority-worker-wrapper.js", "size_bytes": 3578, "size_kb": 3.49, "extension": ".js", "directory": "js", "filename": "priority-worker-wrapper.js", "modified_time": "2025-05-04T09:48:29.064054"}, {"path": "js\\quoteManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\quoteManager.js", "size_bytes": 4317, "size_kb": 4.22, "extension": ".js", "directory": "js", "filename": "quoteManager.js", "modified_time": "2025-05-01T10:31:38.538817"}, {"path": "js\\recipeManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\recipeManager.js", "size_bytes": 19704, "size_kb": 19.24, "extension": ".js", "directory": "js", "filename": "recipeManager.js", "modified_time": "2025-04-23T18:02:07.817814"}, {"path": "js\\reorganize-scripts.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\reorganize-scripts.js", "size_bytes": 4478, "size_kb": 4.37, "extension": ".js", "directory": "js", "filename": "reorganize-scripts.js", "modified_time": "2025-05-01T13:06:32.025669"}, {"path": "js\\roleModelManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\roleModelManager.js", "size_bytes": 10829, "size_kb": 10.58, "extension": ".js", "directory": "js", "filename": "roleModelManager.js", "modified_time": "2025-05-01T10:32:16.139487"}, {"path": "js\\scheduleManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\scheduleManager.js", "size_bytes": 5023, "size_kb": 4.91, "extension": ".js", "directory": "js", "filename": "scheduleManager.js", "modified_time": "2025-04-26T15:39:12.583526"}, {"path": "js\\semester-management.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\semester-management.js", "size_bytes": 62190, "size_kb": 60.73, "extension": ".js", "directory": "js", "filename": "semester-management.js", "modified_time": "2025-04-26T09:35:50.045724"}, {"path": "js\\sideDrawer.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\sideDrawer.js", "size_bytes": 7658, "size_kb": 7.48, "extension": ".js", "directory": "js", "filename": "sideDrawer.js", "modified_time": "2025-04-27T06:17:27.002205"}, {"path": "js\\simulation-enhancer.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\simulation-enhancer.js", "size_bytes": 20114, "size_kb": 19.64, "extension": ".js", "directory": "js", "filename": "simulation-enhancer.js", "modified_time": "2025-04-23T18:02:07.952439"}, {"path": "js\\sleep-saboteurs-init.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\sleep-saboteurs-init.js", "size_bytes": 778, "size_kb": 0.76, "extension": ".js", "directory": "js", "filename": "sleep-saboteurs-init.js", "modified_time": "2025-05-01T12:39:18.454893"}, {"path": "js\\sleepScheduleManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\sleepScheduleManager.js", "size_bytes": 2568, "size_kb": 2.51, "extension": ".js", "directory": "js", "filename": "sleepScheduleManager.js", "modified_time": "2025-04-26T15:19:00.116965"}, {"path": "js\\sleepTimeCalculator.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\sleepTimeCalculator.js", "size_bytes": 2346, "size_kb": 2.29, "extension": ".js", "directory": "js", "filename": "sleepTimeCalculator.js", "modified_time": "2025-04-26T15:12:28.491937"}, {"path": "js\\sm2.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\sm2.js", "size_bytes": 4254, "size_kb": 4.15, "extension": ".js", "directory": "js", "filename": "sm2.js", "modified_time": "2025-04-23T18:02:08.040774"}, {"path": "js\\soundManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\soundManager.js", "size_bytes": 3372, "size_kb": 3.29, "extension": ".js", "directory": "js", "filename": "soundManager.js", "modified_time": "2025-04-23T18:02:08.083198"}, {"path": "js\\speech-recognition.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\speech-recognition.js", "size_bytes": 81174, "size_kb": 79.27, "extension": ".js", "directory": "js", "filename": "speech-recognition.js", "modified_time": "2025-04-26T16:41:33.486207"}, {"path": "js\\speech-synthesis.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\speech-synthesis.js", "size_bytes": 26156, "size_kb": 25.54, "extension": ".js", "directory": "js", "filename": "speech-synthesis.js", "modified_time": "2025-04-26T16:41:33.546261"}, {"path": "js\\storageManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\storageManager.js", "size_bytes": 2014, "size_kb": 1.97, "extension": ".js", "directory": "js", "filename": "storageManager.js", "modified_time": "2025-04-23T18:02:08.225395"}, {"path": "js\\studySpaceAnalyzer.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\studySpaceAnalyzer.js", "size_bytes": 4485, "size_kb": 4.38, "extension": ".js", "directory": "js", "filename": "studySpaceAnalyzer.js", "modified_time": "2025-04-26T15:41:01.418938"}, {"path": "js\\studySpacesFirestore.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\studySpacesFirestore.js", "size_bytes": 8971, "size_kb": 8.76, "extension": ".js", "directory": "js", "filename": "studySpacesFirestore.js", "modified_time": "2025-04-23T18:02:08.270706"}, {"path": "js\\studySpacesManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\studySpacesManager.js", "size_bytes": 57162, "size_kb": 55.82, "extension": ".js", "directory": "js", "filename": "studySpacesManager.js", "modified_time": "2025-04-26T15:12:24.252051"}, {"path": "js\\subject-management.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\subject-management.js", "size_bytes": 23637, "size_kb": 23.08, "extension": ".js", "directory": "js", "filename": "subject-management.js", "modified_time": "2025-04-26T08:56:13.024432"}, {"path": "js\\subject-marks-integration.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\subject-marks-integration.js", "size_bytes": 1277, "size_kb": 1.25, "extension": ".js", "directory": "js", "filename": "subject-marks-integration.js", "modified_time": "2025-04-26T14:33:11.134008"}, {"path": "js\\subject-marks-ui.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\subject-marks-ui.js", "size_bytes": 25657, "size_kb": 25.06, "extension": ".js", "directory": "js", "filename": "subject-marks-ui.js", "modified_time": "2025-04-26T14:33:55.797112"}, {"path": "js\\subject-marks.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\subject-marks.js", "size_bytes": 12652, "size_kb": 12.36, "extension": ".js", "directory": "js", "filename": "subject-marks.js", "modified_time": "2025-04-23T18:02:08.363606"}, {"path": "js\\task-notes-injector.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\task-notes-injector.js", "size_bytes": 6313, "size_kb": 6.17, "extension": ".js", "directory": "js", "filename": "task-notes-injector.js", "modified_time": "2025-04-28T04:38:16.155933"}, {"path": "js\\task-notes.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\task-notes.js", "size_bytes": 25499, "size_kb": 24.9, "extension": ".js", "directory": "js", "filename": "task-notes.js", "modified_time": "2025-05-04T17:31:49.471083"}, {"path": "js\\taskAttachments.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\taskAttachments.js", "size_bytes": 26806, "size_kb": 26.18, "extension": ".js", "directory": "js", "filename": "taskAttachments.js", "modified_time": "2025-04-27T06:17:27.002205"}, {"path": "js\\taskFilters.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\taskFilters.js", "size_bytes": 6515, "size_kb": 6.36, "extension": ".js", "directory": "js", "filename": "taskFilters.js", "modified_time": "2025-04-23T18:02:08.453737"}, {"path": "js\\taskLinks.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\taskLinks.js", "size_bytes": 23567, "size_kb": 23.01, "extension": ".js", "directory": "js", "filename": "taskLinks.js", "modified_time": "2025-05-05T17:16:29.744763"}, {"path": "js\\tasksManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\tasksManager.js", "size_bytes": 8706, "size_kb": 8.5, "extension": ".js", "directory": "js", "filename": "tasksManager.js", "modified_time": "2025-04-23T18:02:08.544591"}, {"path": "js\\test-feedback.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\test-feedback.js", "size_bytes": 53354, "size_kb": 52.1, "extension": ".js", "directory": "js", "filename": "test-feedback.js", "modified_time": "2025-04-27T16:28:51.406620"}, {"path": "js\\text-expansion.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\text-expansion.js", "size_bytes": 26396, "size_kb": 25.78, "extension": ".js", "directory": "js", "filename": "text-expansion.js", "modified_time": "2025-04-23T18:02:08.642238"}, {"path": "js\\theme-manager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\theme-manager.js", "size_bytes": 1830, "size_kb": 1.79, "extension": ".js", "directory": "js", "filename": "theme-manager.js", "modified_time": "2025-05-01T12:38:47.462499"}, {"path": "js\\themeManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\themeManager.js", "size_bytes": 3094, "size_kb": 3.02, "extension": ".js", "directory": "js", "filename": "themeManager.js", "modified_time": "2025-05-01T10:31:20.733387"}, {"path": "js\\timetableAnalyzer.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\timetableAnalyzer.js", "size_bytes": 8930, "size_kb": 8.72, "extension": ".js", "directory": "js", "filename": "timetableAnalyzer.js", "modified_time": "2025-04-26T15:38:22.780367"}, {"path": "js\\timetableIntegration.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\timetableIntegration.js", "size_bytes": 1255, "size_kb": 1.23, "extension": ".js", "directory": "js", "filename": "timetableIntegration.js", "modified_time": "2025-04-23T18:02:08.685275"}, {"path": "js\\todoistIntegration.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\todoistIntegration.js", "size_bytes": 30152, "size_kb": 29.45, "extension": ".js", "directory": "js", "filename": "todoistIntegration.js", "modified_time": "2025-04-23T18:02:08.743218"}, {"path": "js\\transitionManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\transitionManager.js", "size_bytes": 3222, "size_kb": 3.15, "extension": ".js", "directory": "js", "filename": "transitionManager.js", "modified_time": "2025-04-23T18:02:08.787298"}, {"path": "js\\ui-utilities.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\ui-utilities.js", "size_bytes": 5298, "size_kb": 5.17, "extension": ".js", "directory": "js", "filename": "ui-utilities.js", "modified_time": "2025-04-26T08:59:04.300072"}, {"path": "js\\update-html-files.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\update-html-files.js", "size_bytes": 1614, "size_kb": 1.58, "extension": ".js", "directory": "js", "filename": "update-html-files.js", "modified_time": "2025-04-23T18:02:08.831202"}, {"path": "js\\userGuidance.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\userGuidance.js", "size_bytes": 20953, "size_kb": 20.46, "extension": ".js", "directory": "js", "filename": "userGuidance.js", "modified_time": "2025-04-23T18:02:08.878670"}, {"path": "js\\weightage-connector.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\weightage-connector.js", "size_bytes": 9813, "size_kb": 9.58, "extension": ".js", "directory": "js", "filename": "weightage-connector.js", "modified_time": "2025-04-23T18:02:08.924295"}, {"path": "js\\workspace-attachments.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\workspace-attachments.js", "size_bytes": 24543, "size_kb": 23.97, "extension": ".js", "directory": "js", "filename": "workspace-attachments.js", "modified_time": "2025-05-04T18:30:43.470629"}, {"path": "js\\workspace-core.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\workspace-core.js", "size_bytes": 12221, "size_kb": 11.93, "extension": ".js", "directory": "js", "filename": "workspace-core.js", "modified_time": "2025-04-27T03:59:43.771267"}, {"path": "js\\workspace-document.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\workspace-document.js", "size_bytes": 14191, "size_kb": 13.86, "extension": ".js", "directory": "js", "filename": "workspace-document.js", "modified_time": "2025-04-27T03:46:23.198861"}, {"path": "js\\workspace-formatting.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\workspace-formatting.js", "size_bytes": 5203, "size_kb": 5.08, "extension": ".js", "directory": "js", "filename": "workspace-formatting.js", "modified_time": "2025-04-27T03:59:58.221153"}, {"path": "js\\workspace-media.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\workspace-media.js", "size_bytes": 9721, "size_kb": 9.49, "extension": ".js", "directory": "js", "filename": "workspace-media.js", "modified_time": "2025-04-27T03:58:32.171987"}, {"path": "js\\workspace-tables-links.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\workspace-tables-links.js", "size_bytes": 10969, "size_kb": 10.71, "extension": ".js", "directory": "js", "filename": "workspace-tables-links.js", "modified_time": "2025-04-27T03:58:48.060951"}, {"path": "js\\workspace-ui.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\workspace-ui.js", "size_bytes": 4594, "size_kb": 4.49, "extension": ".js", "directory": "js", "filename": "workspace-ui.js", "modified_time": "2025-04-27T03:47:03.653882"}, {"path": "js\\workspaceFlashcardIntegration.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\js\\workspaceFlashcardIntegration.js", "size_bytes": 25930, "size_kb": 25.32, "extension": ".js", "directory": "js", "filename": "workspaceFlashcardIntegration.js", "modified_time": "2025-04-23T18:02:08.970887"}, {"path": "priority-calculator-with-worker.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\priority-calculator-with-worker.js", "size_bytes": 25335, "size_kb": 24.74, "extension": ".js", "directory": ".", "filename": "priority-calculator-with-worker.js", "modified_time": "2025-05-04T09:50:32.765555"}, {"path": "priority-calculator.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\priority-calculator.js", "size_bytes": 26330, "size_kb": 25.71, "extension": ".js", "directory": ".", "filename": "priority-calculator.js", "modified_time": "2025-04-26T10:54:36.580267"}, {"path": "public\\js\\cacheManager.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\public\\js\\cacheManager.js", "size_bytes": 3212, "size_kb": 3.14, "extension": ".js", "directory": "public\\js", "filename": "cacheManager.js", "modified_time": "2025-04-23T18:03:05.345831"}, {"path": "public\\service-worker.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\public\\service-worker.js", "size_bytes": 1, "size_kb": 0.0, "extension": ".js", "directory": "public", "filename": "service-worker.js", "modified_time": "2025-04-23T18:03:05.297381"}, {"path": "relaxed-mode\\script.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\relaxed-mode\\script.js", "size_bytes": 11649, "size_kb": 11.38, "extension": ".js", "directory": "relaxed-mode", "filename": "script.js", "modified_time": "2025-04-23T18:03:05.451899"}, {"path": "scripts\\theme.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\scripts\\theme.js", "size_bytes": 1019, "size_kb": 1.0, "extension": ".js", "directory": "scripts", "filename": "theme.js", "modified_time": "2025-04-23T18:03:05.532807"}, {"path": "server.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\server.js", "size_bytes": 25361, "size_kb": 24.77, "extension": ".js", "directory": ".", "filename": "server.js", "modified_time": "2025-04-23T17:54:26.750403"}, {"path": "server\\dataStorage.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\server\\dataStorage.js", "size_bytes": 6407, "size_kb": 6.26, "extension": ".js", "directory": "server", "filename": "dataStorage.js", "modified_time": "2025-04-23T18:03:05.580912"}, {"path": "server\\routes\\subtasks.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\server\\routes\\subtasks.js", "size_bytes": 2736, "size_kb": 2.67, "extension": ".js", "directory": "server\\routes", "filename": "subtasks.js", "modified_time": "2025-04-23T18:03:05.670066"}, {"path": "server\\timetableHandler.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\server\\timetableHandler.js", "size_bytes": 672, "size_kb": 0.66, "extension": ".js", "directory": "server", "filename": "timetableHandler.js", "modified_time": "2025-04-23T18:03:05.621948"}, {"path": "test-worker.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\test-worker.js", "size_bytes": 3455, "size_kb": 3.37, "extension": ".js", "directory": ".", "filename": "test-worker.js", "modified_time": "2025-05-04T09:48:51.775746"}, {"path": "worker.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\worker.js", "size_bytes": 6619, "size_kb": 6.46, "extension": ".js", "directory": ".", "filename": "worker.js", "modified_time": "2025-05-04T09:48:05.383756"}, {"path": "workers\\imageAnalysis.js", "absolute_path": "E:\\Improving GPAce\\Creating an App\\workers\\imageAnalysis.js", "size_bytes": 7398, "size_kb": 7.22, "extension": ".js", "directory": "workers", "filename": "imageAnalysis.js", "modified_time": "2025-04-23T18:03:13.133215"}]}