{"summary": {"total_files": 161, "largest_files": [{"path": "grind.html", "size": "201061"}, {"path": "grind.css", "size": "125643"}, {"path": "js/ai-researcher.js", "size": "101375"}, {"path": "extracted.html", "size": "92348"}, {"path": "js/speech-recognition.js", "size": "81174"}, {"path": "js/googleDriveApi.js", "size": "72304"}, {"path": "js/semester-management.js", "size": "62190"}, {"path": "js/studySpacesManager.js", "size": "57162"}, {"path": "landing.html", "size": "55202"}, {"path": "js/test-feedback.js", "size": "53354"}, {"path": "js/flashcards.js", "size": "51674"}, {"path": "css/daily-calendar.css", "size": "50181"}, {"path": "js/flashcardManager.js", "size": "49955"}, {"path": "js/calendarManager.js", "size": "43345"}, {"path": "css/workspace.css", "size": "40046"}, {"path": "css/extracted.css", "size": "39627"}, {"path": "js/pomodoroTimer.js", "size": "34711"}, {"path": "js/grind-speech-synthesis.js", "size": "34139"}, {"path": "js/calendar-views.js", "size": "33294"}, {"path": "js/todoistIntegration.js", "size": "30152"}], "current_structure": {"root": [{"path": "404.html", "size": 2114, "type": "html", "category": "view"}, {"path": "academic-details.html", "size": 26146, "type": "html", "category": "view"}, {"path": "daily-calendar.html", "size": 10240, "type": "html", "category": "view"}, {"path": "extracted.html", "size": 92348, "type": "html", "category": "view"}, {"path": "flashcards.html", "size": 16728, "type": "html", "category": "view"}, {"path": "grind.css", "size": 125643, "type": "css", "category": "view_style"}, {"path": "grind.html", "size": 201061, "type": "html", "category": "view"}, {"path": "index.html", "size": 2464, "type": "html", "category": "view"}, {"path": "instant-test-feedback.html", "size": 23222, "type": "html", "category": "view"}, {"path": "landing.html", "size": 55202, "type": "html", "category": "view"}, {"path": "priority-calculator-with-worker.js", "size": 25335, "type": "js", "category": "module"}, {"path": "priority-calculator.html", "size": 7020, "type": "html", "category": "view"}, {"path": "priority-calculator.js", "size": 26330, "type": "js", "category": "module"}, {"path": "priority-list.html", "size": 5227, "type": "html", "category": "view"}, {"path": "server.js", "size": 25361, "type": "js", "category": "module"}, {"path": "settings.html", "size": 5577, "type": "html", "category": "view"}, {"path": "sleep-saboteurs.html", "size": 7732, "type": "html", "category": "view"}, {"path": "study-spaces.html", "size": 21065, "type": "html", "category": "view"}, {"path": "subject-marks.html", "size": 8598, "type": "html", "category": "view"}, {"path": "tasks.html", "size": 24458, "type": "html", "category": "view"}, {"path": "test-worker.js", "size": 3455, "type": "js", "category": "module"}, {"path": "worker.js", "size": 6619, "type": "js", "category": "module"}, {"path": "workspace.html", "size": 20606, "type": "html", "category": "view"}], "css": [{"path": "css/academic-details.css", "size": 27044, "type": "css", "category": "view_style"}, {"path": "css/ai-search-response.css", "size": 12338, "type": "css", "category": "module_style"}, {"path": "css/alarm-service.css", "size": 11193, "type": "css", "category": "module_style"}, {"path": "css/compact-style.css", "size": 4220, "type": "css", "category": "module_style"}, {"path": "css/daily-calendar.css", "size": 50181, "type": "css", "category": "view_style"}, {"path": "css/extracted.css", "size": 39627, "type": "css", "category": "view_style"}, {"path": "css/flashcards.css", "size": 5129, "type": "css", "category": "module_style"}, {"path": "css/notification.css", "size": 967, "type": "css", "category": "module_style"}, {"path": "css/priority-calculator.css", "size": 7623, "type": "css", "category": "module_style"}, {"path": "css/priority-list.css", "size": 10003, "type": "css", "category": "module_style"}, {"path": "css/settings.css", "size": 7412, "type": "css", "category": "module_style"}, {"path": "css/sideDrawer.css", "size": 15934, "type": "css", "category": "module_style"}, {"path": "css/simulation-enhancer.css", "size": 4281, "type": "css", "category": "module_style"}, {"path": "css/sleep-saboteurs.css", "size": 17245, "type": "css", "category": "module_style"}, {"path": "css/study-spaces.css", "size": 19318, "type": "css", "category": "module_style"}, {"path": "css/subject-marks.css", "size": 4989, "type": "css", "category": "module_style"}, {"path": "css/task-display.css", "size": 828, "type": "css", "category": "module_style"}, {"path": "css/task-notes.css", "size": 9506, "type": "css", "category": "module_style"}, {"path": "css/taskLinks.css", "size": 9748, "type": "css", "category": "module_style"}, {"path": "css/test-feedback.css", "size": 16264, "type": "css", "category": "module_style"}, {"path": "css/text-expansion.css", "size": 6288, "type": "css", "category": "module_style"}, {"path": "css/workspace.css", "size": 40046, "type": "css", "category": "view_style"}], "js": [{"path": "js/academic-details.js", "size": 2090, "type": "js", "category": "module"}, {"path": "js/add-favicon.js", "size": 2888, "type": "js", "category": "module"}, {"path": "js/ai-latex-conversion.js", "size": 746, "type": "js", "category": "module"}, {"path": "js/ai-researcher.js", "size": 101375, "type": "js", "category": "view_controller"}, {"path": "js/alarm-data-service.js", "size": 9780, "type": "js", "category": "service"}, {"path": "js/alarm-handler.js", "size": 2032, "type": "js", "category": "module"}, {"path": "js/alarm-mini-display.js", "size": 3702, "type": "js", "category": "component"}, {"path": "js/alarm-service-worker.js", "size": 4761, "type": "js", "category": "service"}, {"path": "js/alarm-service.js", "size": 20505, "type": "js", "category": "service"}, {"path": "js/api-optimization.js", "size": 17304, "type": "js", "category": "service"}, {"path": "js/api-settings.js", "size": 6746, "type": "js", "category": "service"}, {"path": "js/apiSettingsManager.js", "size": 5021, "type": "js", "category": "service"}, {"path": "js/auth.js", "size": 11414, "type": "js", "category": "service"}, {"path": "js/calendar-views.js", "size": 33294, "type": "js", "category": "view_controller"}, {"path": "js/calendarManager.js", "size": 43345, "type": "js", "category": "view_controller"}, {"path": "js/clock-display.js", "size": 2038, "type": "js", "category": "component"}, {"path": "js/common-header.js", "size": 5780, "type": "js", "category": "utility"}, {"path": "js/common.js", "size": 1758, "type": "js", "category": "utility"}, {"path": "js/cross-tab-sync.js", "size": 10833, "type": "js", "category": "integration"}, {"path": "js/currentTaskManager.js", "size": 11858, "type": "js", "category": "service"}, {"path": "js/data-loader.js", "size": 0, "type": "js", "category": "module"}, {"path": "js/data-sync-integration.js", "size": 2027, "type": "js", "category": "integration"}, {"path": "js/data-sync-manager.js", "size": 6643, "type": "js", "category": "service"}, {"path": "js/energyHologram.js", "size": 8670, "type": "js", "category": "module"}, {"path": "js/energyLevels.js", "size": 1616, "type": "js", "category": "module"}, {"path": "js/fileViewer.js", "size": 17164, "type": "js", "category": "module"}, {"path": "js/firebase-config.js", "size": 3200, "type": "js", "category": "service"}, {"path": "js/firebase-init.js", "size": 3040, "type": "js", "category": "module"}, {"path": "js/firebaseAuth.js", "size": 2385, "type": "js", "category": "service"}, {"path": "js/firebaseConfig.js", "size": 1398, "type": "js", "category": "service"}, {"path": "js/firestore-global.js", "size": 1454, "type": "js", "category": "module"}, {"path": "js/firestore.js", "size": 21209, "type": "js", "category": "module"}, {"path": "js/flashcardManager.js", "size": 49955, "type": "js", "category": "view_controller"}, {"path": "js/flashcards.js", "size": 51674, "type": "js", "category": "view_controller"}, {"path": "js/flashcardTaskIntegration.js", "size": 27730, "type": "js", "category": "integration"}, {"path": "js/gemini-api.js", "size": 4024, "type": "js", "category": "service"}, {"path": "js/googleDriveApi.js", "size": 72304, "type": "js", "category": "view_controller"}, {"path": "js/googleGenerativeAI.js", "size": 506, "type": "js", "category": "module"}, {"path": "js/grind-speech-synthesis.js", "size": 34139, "type": "js", "category": "view_controller"}, {"path": "js/imageAnalyzer.js", "size": 8580, "type": "js", "category": "module"}, {"path": "js/indexedDB.js", "size": 0, "type": "js", "category": "module"}, {"path": "js/initFirestoreData.js", "size": 11030, "type": "js", "category": "module"}, {"path": "js/inject-header.js", "size": 2290, "type": "js", "category": "module"}, {"path": "js/markdown-converter.js", "size": 11518, "type": "js", "category": "module"}, {"path": "js/marks-tracking.js", "size": 0, "type": "js", "category": "module"}, {"path": "js/pandoc-fallback.js", "size": 2505, "type": "js", "category": "module"}, {"path": "js/pomodoroGlobal.js", "size": 9682, "type": "js", "category": "module"}, {"path": "js/pomodoroTimer.js", "size": 34711, "type": "js", "category": "view_controller"}, {"path": "js/priority-list-sorting.js", "size": 10551, "type": "js", "category": "module"}, {"path": "js/priority-list-utils.js", "size": 26792, "type": "js", "category": "utility"}, {"path": "js/priority-sync-fix.js", "size": 4836, "type": "js", "category": "integration"}, {"path": "js/priority-worker-wrapper.js", "size": 3578, "type": "js", "category": "module"}, {"path": "js/quoteManager.js", "size": 4317, "type": "js", "category": "service"}, {"path": "js/recipeManager.js", "size": 19704, "type": "js", "category": "service"}, {"path": "js/reorganize-scripts.js", "size": 4478, "type": "js", "category": "module"}, {"path": "js/roleModelManager.js", "size": 10829, "type": "js", "category": "service"}, {"path": "js/scheduleManager.js", "size": 5023, "type": "js", "category": "service"}, {"path": "js/semester-management.js", "size": 62190, "type": "js", "category": "view_controller"}, {"path": "js/sideDrawer.js", "size": 7658, "type": "js", "category": "module"}, {"path": "js/simulation-enhancer.js", "size": 20114, "type": "js", "category": "module"}, {"path": "js/sleep-saboteurs-init.js", "size": 778, "type": "js", "category": "module"}, {"path": "js/sleepScheduleManager.js", "size": 2568, "type": "js", "category": "service"}, {"path": "js/sleepTimeCalculator.js", "size": 2346, "type": "js", "category": "module"}, {"path": "js/sm2.js", "size": 4254, "type": "js", "category": "module"}, {"path": "js/soundManager.js", "size": 3372, "type": "js", "category": "service"}, {"path": "js/speech-recognition.js", "size": 81174, "type": "js", "category": "view_controller"}, {"path": "js/speech-synthesis.js", "size": 26156, "type": "js", "category": "module"}, {"path": "js/storageManager.js", "size": 2014, "type": "js", "category": "service"}, {"path": "js/studySpaceAnalyzer.js", "size": 4485, "type": "js", "category": "module"}, {"path": "js/studySpacesFirestore.js", "size": 8971, "type": "js", "category": "module"}, {"path": "js/studySpacesManager.js", "size": 57162, "type": "js", "category": "view_controller"}, {"path": "js/subject-management.js", "size": 23637, "type": "js", "category": "module"}, {"path": "js/subject-marks-integration.js", "size": 1277, "type": "js", "category": "integration"}, {"path": "js/subject-marks-ui.js", "size": 25657, "type": "js", "category": "component"}, {"path": "js/subject-marks.js", "size": 12652, "type": "js", "category": "module"}, {"path": "js/task-notes-injector.js", "size": 6313, "type": "js", "category": "module"}, {"path": "js/task-notes.js", "size": 25499, "type": "js", "category": "module"}, {"path": "js/taskAttachments.js", "size": 26806, "type": "js", "category": "module"}, {"path": "js/taskFilters.js", "size": 6515, "type": "js", "category": "module"}, {"path": "js/taskLinks.js", "size": 23567, "type": "js", "category": "module"}, {"path": "js/tasksManager.js", "size": 8706, "type": "js", "category": "service"}, {"path": "js/test-feedback.js", "size": 53354, "type": "js", "category": "view_controller"}, {"path": "js/text-expansion.js", "size": 26396, "type": "js", "category": "module"}, {"path": "js/theme-manager.js", "size": 1830, "type": "js", "category": "service"}, {"path": "js/themeManager.js", "size": 3094, "type": "js", "category": "service"}, {"path": "js/timetableAnalyzer.js", "size": 8930, "type": "js", "category": "module"}, {"path": "js/timetableIntegration.js", "size": 1255, "type": "js", "category": "integration"}, {"path": "js/todoistIntegration.js", "size": 30152, "type": "js", "category": "view_controller"}, {"path": "js/transitionManager.js", "size": 3222, "type": "js", "category": "service"}, {"path": "js/ui-utilities.js", "size": 5298, "type": "js", "category": "component"}, {"path": "js/update-html-files.js", "size": 1614, "type": "js", "category": "module"}, {"path": "js/userGuidance.js", "size": 20953, "type": "js", "category": "component"}, {"path": "js/weightage-connector.js", "size": 9813, "type": "js", "category": "integration"}, {"path": "js/workspace-attachments.js", "size": 24543, "type": "js", "category": "module"}, {"path": "js/workspace-core.js", "size": 12221, "type": "js", "category": "module"}, {"path": "js/workspace-document.js", "size": 14191, "type": "js", "category": "module"}, {"path": "js/workspace-formatting.js", "size": 5203, "type": "js", "category": "module"}, {"path": "js/workspace-media.js", "size": 9721, "type": "js", "category": "module"}, {"path": "js/workspace-tables-links.js", "size": 10969, "type": "js", "category": "module"}, {"path": "js/workspace-ui.js", "size": 4594, "type": "js", "category": "component"}, {"path": "js/workspaceFlashcardIntegration.js", "size": 25930, "type": "js", "category": "integration"}], "public\\js": [{"path": "public/js/cacheManager.js", "size": 3212, "type": "js", "category": "service"}], "public": [{"path": "public/service-worker.js", "size": 1, "type": "js", "category": "service"}], "relaxed-mode": [{"path": "relaxed-mode/index.html", "size": 7111, "type": "html", "category": "view"}, {"path": "relaxed-mode/script.js", "size": 11649, "type": "js", "category": "module"}, {"path": "relaxed-mode/style.css", "size": 10525, "type": "css", "category": "module_style"}], "scripts": [{"path": "scripts/theme.js", "size": 1019, "type": "js", "category": "module"}], "server": [{"path": "server/dataStorage.js", "size": 6407, "type": "js", "category": "module"}, {"path": "server/timetableHandler.js", "size": 672, "type": "js", "category": "module"}], "server\\routes": [{"path": "server/routes/subtasks.js", "size": 2736, "type": "js", "category": "module"}], "styles": [{"path": "styles/calendar.css", "size": 9141, "type": "css", "category": "module_style"}, {"path": "styles/index.css", "size": 414, "type": "css", "category": "module_style"}, {"path": "styles/main.css", "size": 15630, "type": "css", "category": "module_style"}, {"path": "styles/study-spaces.css", "size": 3850, "type": "css", "category": "module_style"}, {"path": "styles/tasks.css", "size": 6469, "type": "css", "category": "module_style"}], "workers": [{"path": "workers/imageAnalysis.js", "size": 7398, "type": "js", "category": "module"}]}}, "file_analysis": {"404.html": {"size": 2114, "type": "html", "category": "view", "current_dir": "root"}, "academic-details.html": {"size": 26146, "type": "html", "category": "view", "current_dir": "root"}, "css/academic-details.css": {"size": 27044, "type": "css", "category": "view_style", "current_dir": "css"}, "css/ai-search-response.css": {"size": 12338, "type": "css", "category": "module_style", "current_dir": "css"}, "css/alarm-service.css": {"size": 11193, "type": "css", "category": "module_style", "current_dir": "css"}, "css/compact-style.css": {"size": 4220, "type": "css", "category": "module_style", "current_dir": "css"}, "css/daily-calendar.css": {"size": 50181, "type": "css", "category": "view_style", "current_dir": "css"}, "css/extracted.css": {"size": 39627, "type": "css", "category": "view_style", "current_dir": "css"}, "css/flashcards.css": {"size": 5129, "type": "css", "category": "module_style", "current_dir": "css"}, "css/notification.css": {"size": 967, "type": "css", "category": "module_style", "current_dir": "css"}, "css/priority-calculator.css": {"size": 7623, "type": "css", "category": "module_style", "current_dir": "css"}, "css/priority-list.css": {"size": 10003, "type": "css", "category": "module_style", "current_dir": "css"}, "css/settings.css": {"size": 7412, "type": "css", "category": "module_style", "current_dir": "css"}, "css/sideDrawer.css": {"size": 15934, "type": "css", "category": "module_style", "current_dir": "css"}, "css/simulation-enhancer.css": {"size": 4281, "type": "css", "category": "module_style", "current_dir": "css"}, "css/sleep-saboteurs.css": {"size": 17245, "type": "css", "category": "module_style", "current_dir": "css"}, "css/study-spaces.css": {"size": 19318, "type": "css", "category": "module_style", "current_dir": "css"}, "css/subject-marks.css": {"size": 4989, "type": "css", "category": "module_style", "current_dir": "css"}, "css/task-display.css": {"size": 828, "type": "css", "category": "module_style", "current_dir": "css"}, "css/task-notes.css": {"size": 9506, "type": "css", "category": "module_style", "current_dir": "css"}, "css/taskLinks.css": {"size": 9748, "type": "css", "category": "module_style", "current_dir": "css"}, "css/test-feedback.css": {"size": 16264, "type": "css", "category": "module_style", "current_dir": "css"}, "css/text-expansion.css": {"size": 6288, "type": "css", "category": "module_style", "current_dir": "css"}, "css/workspace.css": {"size": 40046, "type": "css", "category": "view_style", "current_dir": "css"}, "daily-calendar.html": {"size": 10240, "type": "html", "category": "view", "current_dir": "root"}, "extracted.html": {"size": 92348, "type": "html", "category": "view", "current_dir": "root"}, "flashcards.html": {"size": 16728, "type": "html", "category": "view", "current_dir": "root"}, "grind.css": {"size": 125643, "type": "css", "category": "view_style", "current_dir": "root"}, "grind.html": {"size": 201061, "type": "html", "category": "view", "current_dir": "root"}, "index.html": {"size": 2464, "type": "html", "category": "view", "current_dir": "root"}, "instant-test-feedback.html": {"size": 23222, "type": "html", "category": "view", "current_dir": "root"}, "js/academic-details.js": {"size": 2090, "type": "js", "category": "module", "current_dir": "js"}, "js/add-favicon.js": {"size": 2888, "type": "js", "category": "module", "current_dir": "js"}, "js/ai-latex-conversion.js": {"size": 746, "type": "js", "category": "module", "current_dir": "js"}, "js/ai-researcher.js": {"size": 101375, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/alarm-data-service.js": {"size": 9780, "type": "js", "category": "service", "current_dir": "js"}, "js/alarm-handler.js": {"size": 2032, "type": "js", "category": "module", "current_dir": "js"}, "js/alarm-mini-display.js": {"size": 3702, "type": "js", "category": "component", "current_dir": "js"}, "js/alarm-service-worker.js": {"size": 4761, "type": "js", "category": "service", "current_dir": "js"}, "js/alarm-service.js": {"size": 20505, "type": "js", "category": "service", "current_dir": "js"}, "js/api-optimization.js": {"size": 17304, "type": "js", "category": "service", "current_dir": "js"}, "js/api-settings.js": {"size": 6746, "type": "js", "category": "service", "current_dir": "js"}, "js/apiSettingsManager.js": {"size": 5021, "type": "js", "category": "service", "current_dir": "js"}, "js/auth.js": {"size": 11414, "type": "js", "category": "service", "current_dir": "js"}, "js/calendar-views.js": {"size": 33294, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/calendarManager.js": {"size": 43345, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/clock-display.js": {"size": 2038, "type": "js", "category": "component", "current_dir": "js"}, "js/common-header.js": {"size": 5780, "type": "js", "category": "utility", "current_dir": "js"}, "js/common.js": {"size": 1758, "type": "js", "category": "utility", "current_dir": "js"}, "js/cross-tab-sync.js": {"size": 10833, "type": "js", "category": "integration", "current_dir": "js"}, "js/currentTaskManager.js": {"size": 11858, "type": "js", "category": "service", "current_dir": "js"}, "js/data-loader.js": {"size": 0, "type": "js", "category": "module", "current_dir": "js"}, "js/data-sync-integration.js": {"size": 2027, "type": "js", "category": "integration", "current_dir": "js"}, "js/data-sync-manager.js": {"size": 6643, "type": "js", "category": "service", "current_dir": "js"}, "js/energyHologram.js": {"size": 8670, "type": "js", "category": "module", "current_dir": "js"}, "js/energyLevels.js": {"size": 1616, "type": "js", "category": "module", "current_dir": "js"}, "js/fileViewer.js": {"size": 17164, "type": "js", "category": "module", "current_dir": "js"}, "js/firebase-config.js": {"size": 3200, "type": "js", "category": "service", "current_dir": "js"}, "js/firebase-init.js": {"size": 3040, "type": "js", "category": "module", "current_dir": "js"}, "js/firebaseAuth.js": {"size": 2385, "type": "js", "category": "service", "current_dir": "js"}, "js/firebaseConfig.js": {"size": 1398, "type": "js", "category": "service", "current_dir": "js"}, "js/firestore-global.js": {"size": 1454, "type": "js", "category": "module", "current_dir": "js"}, "js/firestore.js": {"size": 21209, "type": "js", "category": "module", "current_dir": "js"}, "js/flashcardManager.js": {"size": 49955, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/flashcards.js": {"size": 51674, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/flashcardTaskIntegration.js": {"size": 27730, "type": "js", "category": "integration", "current_dir": "js"}, "js/gemini-api.js": {"size": 4024, "type": "js", "category": "service", "current_dir": "js"}, "js/googleDriveApi.js": {"size": 72304, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/googleGenerativeAI.js": {"size": 506, "type": "js", "category": "module", "current_dir": "js"}, "js/grind-speech-synthesis.js": {"size": 34139, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/imageAnalyzer.js": {"size": 8580, "type": "js", "category": "module", "current_dir": "js"}, "js/indexedDB.js": {"size": 0, "type": "js", "category": "module", "current_dir": "js"}, "js/initFirestoreData.js": {"size": 11030, "type": "js", "category": "module", "current_dir": "js"}, "js/inject-header.js": {"size": 2290, "type": "js", "category": "module", "current_dir": "js"}, "js/markdown-converter.js": {"size": 11518, "type": "js", "category": "module", "current_dir": "js"}, "js/marks-tracking.js": {"size": 0, "type": "js", "category": "module", "current_dir": "js"}, "js/pandoc-fallback.js": {"size": 2505, "type": "js", "category": "module", "current_dir": "js"}, "js/pomodoroGlobal.js": {"size": 9682, "type": "js", "category": "module", "current_dir": "js"}, "js/pomodoroTimer.js": {"size": 34711, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/priority-list-sorting.js": {"size": 10551, "type": "js", "category": "module", "current_dir": "js"}, "js/priority-list-utils.js": {"size": 26792, "type": "js", "category": "utility", "current_dir": "js"}, "js/priority-sync-fix.js": {"size": 4836, "type": "js", "category": "integration", "current_dir": "js"}, "js/priority-worker-wrapper.js": {"size": 3578, "type": "js", "category": "module", "current_dir": "js"}, "js/quoteManager.js": {"size": 4317, "type": "js", "category": "service", "current_dir": "js"}, "js/recipeManager.js": {"size": 19704, "type": "js", "category": "service", "current_dir": "js"}, "js/reorganize-scripts.js": {"size": 4478, "type": "js", "category": "module", "current_dir": "js"}, "js/roleModelManager.js": {"size": 10829, "type": "js", "category": "service", "current_dir": "js"}, "js/scheduleManager.js": {"size": 5023, "type": "js", "category": "service", "current_dir": "js"}, "js/semester-management.js": {"size": 62190, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/sideDrawer.js": {"size": 7658, "type": "js", "category": "module", "current_dir": "js"}, "js/simulation-enhancer.js": {"size": 20114, "type": "js", "category": "module", "current_dir": "js"}, "js/sleep-saboteurs-init.js": {"size": 778, "type": "js", "category": "module", "current_dir": "js"}, "js/sleepScheduleManager.js": {"size": 2568, "type": "js", "category": "service", "current_dir": "js"}, "js/sleepTimeCalculator.js": {"size": 2346, "type": "js", "category": "module", "current_dir": "js"}, "js/sm2.js": {"size": 4254, "type": "js", "category": "module", "current_dir": "js"}, "js/soundManager.js": {"size": 3372, "type": "js", "category": "service", "current_dir": "js"}, "js/speech-recognition.js": {"size": 81174, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/speech-synthesis.js": {"size": 26156, "type": "js", "category": "module", "current_dir": "js"}, "js/storageManager.js": {"size": 2014, "type": "js", "category": "service", "current_dir": "js"}, "js/studySpaceAnalyzer.js": {"size": 4485, "type": "js", "category": "module", "current_dir": "js"}, "js/studySpacesFirestore.js": {"size": 8971, "type": "js", "category": "module", "current_dir": "js"}, "js/studySpacesManager.js": {"size": 57162, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/subject-management.js": {"size": 23637, "type": "js", "category": "module", "current_dir": "js"}, "js/subject-marks-integration.js": {"size": 1277, "type": "js", "category": "integration", "current_dir": "js"}, "js/subject-marks-ui.js": {"size": 25657, "type": "js", "category": "component", "current_dir": "js"}, "js/subject-marks.js": {"size": 12652, "type": "js", "category": "module", "current_dir": "js"}, "js/task-notes-injector.js": {"size": 6313, "type": "js", "category": "module", "current_dir": "js"}, "js/task-notes.js": {"size": 25499, "type": "js", "category": "module", "current_dir": "js"}, "js/taskAttachments.js": {"size": 26806, "type": "js", "category": "module", "current_dir": "js"}, "js/taskFilters.js": {"size": 6515, "type": "js", "category": "module", "current_dir": "js"}, "js/taskLinks.js": {"size": 23567, "type": "js", "category": "module", "current_dir": "js"}, "js/tasksManager.js": {"size": 8706, "type": "js", "category": "service", "current_dir": "js"}, "js/test-feedback.js": {"size": 53354, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/text-expansion.js": {"size": 26396, "type": "js", "category": "module", "current_dir": "js"}, "js/theme-manager.js": {"size": 1830, "type": "js", "category": "service", "current_dir": "js"}, "js/themeManager.js": {"size": 3094, "type": "js", "category": "service", "current_dir": "js"}, "js/timetableAnalyzer.js": {"size": 8930, "type": "js", "category": "module", "current_dir": "js"}, "js/timetableIntegration.js": {"size": 1255, "type": "js", "category": "integration", "current_dir": "js"}, "js/todoistIntegration.js": {"size": 30152, "type": "js", "category": "view_controller", "current_dir": "js"}, "js/transitionManager.js": {"size": 3222, "type": "js", "category": "service", "current_dir": "js"}, "js/ui-utilities.js": {"size": 5298, "type": "js", "category": "component", "current_dir": "js"}, "js/update-html-files.js": {"size": 1614, "type": "js", "category": "module", "current_dir": "js"}, "js/userGuidance.js": {"size": 20953, "type": "js", "category": "component", "current_dir": "js"}, "js/weightage-connector.js": {"size": 9813, "type": "js", "category": "integration", "current_dir": "js"}, "js/workspace-attachments.js": {"size": 24543, "type": "js", "category": "module", "current_dir": "js"}, "js/workspace-core.js": {"size": 12221, "type": "js", "category": "module", "current_dir": "js"}, "js/workspace-document.js": {"size": 14191, "type": "js", "category": "module", "current_dir": "js"}, "js/workspace-formatting.js": {"size": 5203, "type": "js", "category": "module", "current_dir": "js"}, "js/workspace-media.js": {"size": 9721, "type": "js", "category": "module", "current_dir": "js"}, "js/workspace-tables-links.js": {"size": 10969, "type": "js", "category": "module", "current_dir": "js"}, "js/workspace-ui.js": {"size": 4594, "type": "js", "category": "component", "current_dir": "js"}, "js/workspaceFlashcardIntegration.js": {"size": 25930, "type": "js", "category": "integration", "current_dir": "js"}, "landing.html": {"size": 55202, "type": "html", "category": "view", "current_dir": "root"}, "priority-calculator-with-worker.js": {"size": 25335, "type": "js", "category": "module", "current_dir": "root"}, "priority-calculator.html": {"size": 7020, "type": "html", "category": "view", "current_dir": "root"}, "priority-calculator.js": {"size": 26330, "type": "js", "category": "module", "current_dir": "root"}, "priority-list.html": {"size": 5227, "type": "html", "category": "view", "current_dir": "root"}, "public/js/cacheManager.js": {"size": 3212, "type": "js", "category": "service", "current_dir": "public\\js"}, "public/service-worker.js": {"size": 1, "type": "js", "category": "service", "current_dir": "public"}, "relaxed-mode/index.html": {"size": 7111, "type": "html", "category": "view", "current_dir": "relaxed-mode"}, "relaxed-mode/script.js": {"size": 11649, "type": "js", "category": "module", "current_dir": "relaxed-mode"}, "relaxed-mode/style.css": {"size": 10525, "type": "css", "category": "module_style", "current_dir": "relaxed-mode"}, "scripts/theme.js": {"size": 1019, "type": "js", "category": "module", "current_dir": "scripts"}, "server/dataStorage.js": {"size": 6407, "type": "js", "category": "module", "current_dir": "server"}, "server/routes/subtasks.js": {"size": 2736, "type": "js", "category": "module", "current_dir": "server\\routes"}, "server/timetableHandler.js": {"size": 672, "type": "js", "category": "module", "current_dir": "server"}, "server.js": {"size": 25361, "type": "js", "category": "module", "current_dir": "root"}, "settings.html": {"size": 5577, "type": "html", "category": "view", "current_dir": "root"}, "sleep-saboteurs.html": {"size": 7732, "type": "html", "category": "view", "current_dir": "root"}, "study-spaces.html": {"size": 21065, "type": "html", "category": "view", "current_dir": "root"}, "styles/calendar.css": {"size": 9141, "type": "css", "category": "module_style", "current_dir": "styles"}, "styles/index.css": {"size": 414, "type": "css", "category": "module_style", "current_dir": "styles"}, "styles/main.css": {"size": 15630, "type": "css", "category": "module_style", "current_dir": "styles"}, "styles/study-spaces.css": {"size": 3850, "type": "css", "category": "module_style", "current_dir": "styles"}, "styles/tasks.css": {"size": 6469, "type": "css", "category": "module_style", "current_dir": "styles"}, "subject-marks.html": {"size": 8598, "type": "html", "category": "view", "current_dir": "root"}, "tasks.html": {"size": 24458, "type": "html", "category": "view", "current_dir": "root"}, "test-worker.js": {"size": 3455, "type": "js", "category": "module", "current_dir": "root"}, "worker.js": {"size": 6619, "type": "js", "category": "module", "current_dir": "root"}, "workers/imageAnalysis.js": {"size": 7398, "type": "js", "category": "module", "current_dir": "workers"}, "workspace.html": {"size": 20606, "type": "html", "category": "view", "current_dir": "root"}}, "proposed_structure": {"src": {"views": {}, "components": {}, "layouts": {}, "services": {}, "utils": {}, "integrations": {}, "styles": {"views": {}, "components": {}, "layouts": {}, "base": {}}, "scripts": {"views": {}, "components": {}, "services": {}, "utils": {}}, "assets": {"images": {}, "icons": {}, "sounds": {}, "fonts": {}}}, "public": {}, "server": {}, "config": {}, "data": {}, "docs": {}, "file_mappings": {"404.html": {"target_path": "src/views/404.html", "size": 2114, "type": "html", "category": "view"}, "academic-details.html": {"target_path": "src/views/academic-details.html", "size": 26146, "type": "html", "category": "view"}, "css/academic-details.css": {"target_path": "src/styles/views/academic-details.css", "size": 27044, "type": "css", "category": "view_style"}, "css/ai-search-response.css": {"target_path": "src/styles/base/ai-search-response.css", "size": 12338, "type": "css", "category": "module_style"}, "css/alarm-service.css": {"target_path": "src/styles/base/alarm-service.css", "size": 11193, "type": "css", "category": "module_style"}, "css/compact-style.css": {"target_path": "src/styles/base/compact-style.css", "size": 4220, "type": "css", "category": "module_style"}, "css/daily-calendar.css": {"target_path": "src/styles/views/daily-calendar.css", "size": 50181, "type": "css", "category": "view_style"}, "css/extracted.css": {"target_path": "src/styles/views/extracted.css", "size": 39627, "type": "css", "category": "view_style"}, "css/flashcards.css": {"target_path": "src/styles/base/flashcards.css", "size": 5129, "type": "css", "category": "module_style"}, "css/notification.css": {"target_path": "src/styles/base/notification.css", "size": 967, "type": "css", "category": "module_style"}, "css/priority-calculator.css": {"target_path": "src/styles/base/priority-calculator.css", "size": 7623, "type": "css", "category": "module_style"}, "css/priority-list.css": {"target_path": "src/styles/base/priority-list.css", "size": 10003, "type": "css", "category": "module_style"}, "css/settings.css": {"target_path": "src/styles/base/settings.css", "size": 7412, "type": "css", "category": "module_style"}, "css/sideDrawer.css": {"target_path": "src/styles/base/sideDrawer.css", "size": 15934, "type": "css", "category": "module_style"}, "css/simulation-enhancer.css": {"target_path": "src/styles/base/simulation-enhancer.css", "size": 4281, "type": "css", "category": "module_style"}, "css/sleep-saboteurs.css": {"target_path": "src/styles/base/sleep-saboteurs.css", "size": 17245, "type": "css", "category": "module_style"}, "css/study-spaces.css": {"target_path": "src/styles/base/study-spaces.css", "size": 19318, "type": "css", "category": "module_style"}, "css/subject-marks.css": {"target_path": "src/styles/base/subject-marks.css", "size": 4989, "type": "css", "category": "module_style"}, "css/task-display.css": {"target_path": "src/styles/base/task-display.css", "size": 828, "type": "css", "category": "module_style"}, "css/task-notes.css": {"target_path": "src/styles/base/task-notes.css", "size": 9506, "type": "css", "category": "module_style"}, "css/taskLinks.css": {"target_path": "src/styles/base/taskLinks.css", "size": 9748, "type": "css", "category": "module_style"}, "css/test-feedback.css": {"target_path": "src/styles/base/test-feedback.css", "size": 16264, "type": "css", "category": "module_style"}, "css/text-expansion.css": {"target_path": "src/styles/base/text-expansion.css", "size": 6288, "type": "css", "category": "module_style"}, "css/workspace.css": {"target_path": "src/styles/views/workspace.css", "size": 40046, "type": "css", "category": "view_style"}, "daily-calendar.html": {"target_path": "src/views/daily-calendar.html", "size": 10240, "type": "html", "category": "view"}, "extracted.html": {"target_path": "src/views/extracted.html", "size": 92348, "type": "html", "category": "view"}, "flashcards.html": {"target_path": "src/views/flashcards.html", "size": 16728, "type": "html", "category": "view"}, "grind.css": {"target_path": "src/styles/views/grind.css", "size": 125643, "type": "css", "category": "view_style"}, "grind.html": {"target_path": "src/views/grind.html", "size": 201061, "type": "html", "category": "view"}, "index.html": {"target_path": "src/views/index.html", "size": 2464, "type": "html", "category": "view"}, "instant-test-feedback.html": {"target_path": "src/views/instant-test-feedback.html", "size": 23222, "type": "html", "category": "view"}, "js/academic-details.js": {"target_path": "src/scripts/academic-details.js", "size": 2090, "type": "js", "category": "module"}, "js/add-favicon.js": {"target_path": "src/scripts/add-favicon.js", "size": 2888, "type": "js", "category": "module"}, "js/ai-latex-conversion.js": {"target_path": "src/scripts/ai-latex-conversion.js", "size": 746, "type": "js", "category": "module"}, "js/ai-researcher.js": {"target_path": "src/scripts/views/ai-researcher.js", "size": 101375, "type": "js", "category": "view_controller"}, "js/alarm-data-service.js": {"target_path": "src/services/alarm-data-service.js", "size": 9780, "type": "js", "category": "service"}, "js/alarm-handler.js": {"target_path": "src/scripts/alarm-handler.js", "size": 2032, "type": "js", "category": "module"}, "js/alarm-mini-display.js": {"target_path": "src/scripts/components/alarm-mini-display.js", "size": 3702, "type": "js", "category": "component"}, "js/alarm-service-worker.js": {"target_path": "src/services/alarm-service-worker.js", "size": 4761, "type": "js", "category": "service"}, "js/alarm-service.js": {"target_path": "src/services/alarm-service.js", "size": 20505, "type": "js", "category": "service"}, "js/api-optimization.js": {"target_path": "src/services/api-optimization.js", "size": 17304, "type": "js", "category": "service"}, "js/api-settings.js": {"target_path": "src/services/api-settings.js", "size": 6746, "type": "js", "category": "service"}, "js/apiSettingsManager.js": {"target_path": "src/services/apiSettingsManager.js", "size": 5021, "type": "js", "category": "service"}, "js/auth.js": {"target_path": "src/services/auth.js", "size": 11414, "type": "js", "category": "service"}, "js/calendar-views.js": {"target_path": "src/scripts/views/calendar-views.js", "size": 33294, "type": "js", "category": "view_controller"}, "js/calendarManager.js": {"target_path": "src/scripts/views/calendarManager.js", "size": 43345, "type": "js", "category": "view_controller"}, "js/clock-display.js": {"target_path": "src/scripts/components/clock-display.js", "size": 2038, "type": "js", "category": "component"}, "js/common-header.js": {"target_path": "src/utils/common-header.js", "size": 5780, "type": "js", "category": "utility"}, "js/common.js": {"target_path": "src/utils/common.js", "size": 1758, "type": "js", "category": "utility"}, "js/cross-tab-sync.js": {"target_path": "src/integrations/cross-tab-sync.js", "size": 10833, "type": "js", "category": "integration"}, "js/currentTaskManager.js": {"target_path": "src/services/currentTaskManager.js", "size": 11858, "type": "js", "category": "service"}, "js/data-loader.js": {"target_path": "src/scripts/data-loader.js", "size": 0, "type": "js", "category": "module"}, "js/data-sync-integration.js": {"target_path": "src/integrations/data-sync-integration.js", "size": 2027, "type": "js", "category": "integration"}, "js/data-sync-manager.js": {"target_path": "src/services/data-sync-manager.js", "size": 6643, "type": "js", "category": "service"}, "js/energyHologram.js": {"target_path": "src/scripts/energyHologram.js", "size": 8670, "type": "js", "category": "module"}, "js/energyLevels.js": {"target_path": "src/scripts/energyLevels.js", "size": 1616, "type": "js", "category": "module"}, "js/fileViewer.js": {"target_path": "src/scripts/fileViewer.js", "size": 17164, "type": "js", "category": "module"}, "js/firebase-config.js": {"target_path": "src/services/firebase-config.js", "size": 3200, "type": "js", "category": "service"}, "js/firebase-init.js": {"target_path": "src/scripts/firebase-init.js", "size": 3040, "type": "js", "category": "module"}, "js/firebaseAuth.js": {"target_path": "src/services/firebaseAuth.js", "size": 2385, "type": "js", "category": "service"}, "js/firebaseConfig.js": {"target_path": "src/services/firebaseConfig.js", "size": 1398, "type": "js", "category": "service"}, "js/firestore-global.js": {"target_path": "src/scripts/firestore-global.js", "size": 1454, "type": "js", "category": "module"}, "js/firestore.js": {"target_path": "src/scripts/firestore.js", "size": 21209, "type": "js", "category": "module"}, "js/flashcardManager.js": {"target_path": "src/scripts/views/flashcardManager.js", "size": 49955, "type": "js", "category": "view_controller"}, "js/flashcards.js": {"target_path": "src/scripts/views/flashcards.js", "size": 51674, "type": "js", "category": "view_controller"}, "js/flashcardTaskIntegration.js": {"target_path": "src/integrations/flashcardTaskIntegration.js", "size": 27730, "type": "js", "category": "integration"}, "js/gemini-api.js": {"target_path": "src/services/gemini-api.js", "size": 4024, "type": "js", "category": "service"}, "js/googleDriveApi.js": {"target_path": "src/scripts/views/googleDriveApi.js", "size": 72304, "type": "js", "category": "view_controller"}, "js/googleGenerativeAI.js": {"target_path": "src/scripts/googleGenerativeAI.js", "size": 506, "type": "js", "category": "module"}, "js/grind-speech-synthesis.js": {"target_path": "src/scripts/views/grind-speech-synthesis.js", "size": 34139, "type": "js", "category": "view_controller"}, "js/imageAnalyzer.js": {"target_path": "src/scripts/imageAnalyzer.js", "size": 8580, "type": "js", "category": "module"}, "js/indexedDB.js": {"target_path": "src/scripts/indexedDB.js", "size": 0, "type": "js", "category": "module"}, "js/initFirestoreData.js": {"target_path": "src/scripts/initFirestoreData.js", "size": 11030, "type": "js", "category": "module"}, "js/inject-header.js": {"target_path": "src/scripts/inject-header.js", "size": 2290, "type": "js", "category": "module"}, "js/markdown-converter.js": {"target_path": "src/scripts/markdown-converter.js", "size": 11518, "type": "js", "category": "module"}, "js/marks-tracking.js": {"target_path": "src/scripts/marks-tracking.js", "size": 0, "type": "js", "category": "module"}, "js/pandoc-fallback.js": {"target_path": "src/scripts/pandoc-fallback.js", "size": 2505, "type": "js", "category": "module"}, "js/pomodoroGlobal.js": {"target_path": "src/scripts/pomodoroGlobal.js", "size": 9682, "type": "js", "category": "module"}, "js/pomodoroTimer.js": {"target_path": "src/scripts/views/pomodoroTimer.js", "size": 34711, "type": "js", "category": "view_controller"}, "js/priority-list-sorting.js": {"target_path": "src/scripts/priority-list-sorting.js", "size": 10551, "type": "js", "category": "module"}, "js/priority-list-utils.js": {"target_path": "src/utils/priority-list-utils.js", "size": 26792, "type": "js", "category": "utility"}, "js/priority-sync-fix.js": {"target_path": "src/integrations/priority-sync-fix.js", "size": 4836, "type": "js", "category": "integration"}, "js/priority-worker-wrapper.js": {"target_path": "src/scripts/priority-worker-wrapper.js", "size": 3578, "type": "js", "category": "module"}, "js/quoteManager.js": {"target_path": "src/services/quoteManager.js", "size": 4317, "type": "js", "category": "service"}, "js/recipeManager.js": {"target_path": "src/services/recipeManager.js", "size": 19704, "type": "js", "category": "service"}, "js/reorganize-scripts.js": {"target_path": "src/scripts/reorganize-scripts.js", "size": 4478, "type": "js", "category": "module"}, "js/roleModelManager.js": {"target_path": "src/services/roleModelManager.js", "size": 10829, "type": "js", "category": "service"}, "js/scheduleManager.js": {"target_path": "src/services/scheduleManager.js", "size": 5023, "type": "js", "category": "service"}, "js/semester-management.js": {"target_path": "src/scripts/views/semester-management.js", "size": 62190, "type": "js", "category": "view_controller"}, "js/sideDrawer.js": {"target_path": "src/scripts/sideDrawer.js", "size": 7658, "type": "js", "category": "module"}, "js/simulation-enhancer.js": {"target_path": "src/scripts/simulation-enhancer.js", "size": 20114, "type": "js", "category": "module"}, "js/sleep-saboteurs-init.js": {"target_path": "src/scripts/sleep-saboteurs-init.js", "size": 778, "type": "js", "category": "module"}, "js/sleepScheduleManager.js": {"target_path": "src/services/sleepScheduleManager.js", "size": 2568, "type": "js", "category": "service"}, "js/sleepTimeCalculator.js": {"target_path": "src/scripts/sleepTimeCalculator.js", "size": 2346, "type": "js", "category": "module"}, "js/sm2.js": {"target_path": "src/scripts/sm2.js", "size": 4254, "type": "js", "category": "module"}, "js/soundManager.js": {"target_path": "src/services/soundManager.js", "size": 3372, "type": "js", "category": "service"}, "js/speech-recognition.js": {"target_path": "src/scripts/views/speech-recognition.js", "size": 81174, "type": "js", "category": "view_controller"}, "js/speech-synthesis.js": {"target_path": "src/scripts/speech-synthesis.js", "size": 26156, "type": "js", "category": "module"}, "js/storageManager.js": {"target_path": "src/services/storageManager.js", "size": 2014, "type": "js", "category": "service"}, "js/studySpaceAnalyzer.js": {"target_path": "src/scripts/studySpaceAnalyzer.js", "size": 4485, "type": "js", "category": "module"}, "js/studySpacesFirestore.js": {"target_path": "src/scripts/studySpacesFirestore.js", "size": 8971, "type": "js", "category": "module"}, "js/studySpacesManager.js": {"target_path": "src/scripts/views/studySpacesManager.js", "size": 57162, "type": "js", "category": "view_controller"}, "js/subject-management.js": {"target_path": "src/scripts/subject-management.js", "size": 23637, "type": "js", "category": "module"}, "js/subject-marks-integration.js": {"target_path": "src/integrations/subject-marks-integration.js", "size": 1277, "type": "js", "category": "integration"}, "js/subject-marks-ui.js": {"target_path": "src/scripts/components/subject-marks-ui.js", "size": 25657, "type": "js", "category": "component"}, "js/subject-marks.js": {"target_path": "src/scripts/subject-marks.js", "size": 12652, "type": "js", "category": "module"}, "js/task-notes-injector.js": {"target_path": "src/scripts/task-notes-injector.js", "size": 6313, "type": "js", "category": "module"}, "js/task-notes.js": {"target_path": "src/scripts/task-notes.js", "size": 25499, "type": "js", "category": "module"}, "js/taskAttachments.js": {"target_path": "src/scripts/taskAttachments.js", "size": 26806, "type": "js", "category": "module"}, "js/taskFilters.js": {"target_path": "src/scripts/taskFilters.js", "size": 6515, "type": "js", "category": "module"}, "js/taskLinks.js": {"target_path": "src/scripts/taskLinks.js", "size": 23567, "type": "js", "category": "module"}, "js/tasksManager.js": {"target_path": "src/services/tasksManager.js", "size": 8706, "type": "js", "category": "service"}, "js/test-feedback.js": {"target_path": "src/scripts/views/test-feedback.js", "size": 53354, "type": "js", "category": "view_controller"}, "js/text-expansion.js": {"target_path": "src/scripts/text-expansion.js", "size": 26396, "type": "js", "category": "module"}, "js/theme-manager.js": {"target_path": "src/services/theme-manager.js", "size": 1830, "type": "js", "category": "service"}, "js/themeManager.js": {"target_path": "src/services/themeManager.js", "size": 3094, "type": "js", "category": "service"}, "js/timetableAnalyzer.js": {"target_path": "src/scripts/timetableAnalyzer.js", "size": 8930, "type": "js", "category": "module"}, "js/timetableIntegration.js": {"target_path": "src/integrations/timetableIntegration.js", "size": 1255, "type": "js", "category": "integration"}, "js/todoistIntegration.js": {"target_path": "src/scripts/views/todoistIntegration.js", "size": 30152, "type": "js", "category": "view_controller"}, "js/transitionManager.js": {"target_path": "src/services/transitionManager.js", "size": 3222, "type": "js", "category": "service"}, "js/ui-utilities.js": {"target_path": "src/scripts/components/ui-utilities.js", "size": 5298, "type": "js", "category": "component"}, "js/update-html-files.js": {"target_path": "src/scripts/update-html-files.js", "size": 1614, "type": "js", "category": "module"}, "js/userGuidance.js": {"target_path": "src/scripts/components/userGuidance.js", "size": 20953, "type": "js", "category": "component"}, "js/weightage-connector.js": {"target_path": "src/integrations/weightage-connector.js", "size": 9813, "type": "js", "category": "integration"}, "js/workspace-attachments.js": {"target_path": "src/scripts/workspace-attachments.js", "size": 24543, "type": "js", "category": "module"}, "js/workspace-core.js": {"target_path": "src/scripts/workspace-core.js", "size": 12221, "type": "js", "category": "module"}, "js/workspace-document.js": {"target_path": "src/scripts/workspace-document.js", "size": 14191, "type": "js", "category": "module"}, "js/workspace-formatting.js": {"target_path": "src/scripts/workspace-formatting.js", "size": 5203, "type": "js", "category": "module"}, "js/workspace-media.js": {"target_path": "src/scripts/workspace-media.js", "size": 9721, "type": "js", "category": "module"}, "js/workspace-tables-links.js": {"target_path": "src/scripts/workspace-tables-links.js", "size": 10969, "type": "js", "category": "module"}, "js/workspace-ui.js": {"target_path": "src/scripts/components/workspace-ui.js", "size": 4594, "type": "js", "category": "component"}, "js/workspaceFlashcardIntegration.js": {"target_path": "src/integrations/workspaceFlashcardIntegration.js", "size": 25930, "type": "js", "category": "integration"}, "landing.html": {"target_path": "src/views/landing.html", "size": 55202, "type": "html", "category": "view"}, "priority-calculator-with-worker.js": {"target_path": "src/scripts/priority-calculator-with-worker.js", "size": 25335, "type": "js", "category": "module"}, "priority-calculator.html": {"target_path": "src/views/priority-calculator.html", "size": 7020, "type": "html", "category": "view"}, "priority-calculator.js": {"target_path": "src/scripts/priority-calculator.js", "size": 26330, "type": "js", "category": "module"}, "priority-list.html": {"target_path": "src/views/priority-list.html", "size": 5227, "type": "html", "category": "view"}, "public/js/cacheManager.js": {"target_path": "src/services/cacheManager.js", "size": 3212, "type": "js", "category": "service"}, "public/service-worker.js": {"target_path": "src/services/service-worker.js", "size": 1, "type": "js", "category": "service"}, "relaxed-mode/index.html": {"target_path": "src/views/index.html", "size": 7111, "type": "html", "category": "view"}, "relaxed-mode/script.js": {"target_path": "src/scripts/script.js", "size": 11649, "type": "js", "category": "module"}, "relaxed-mode/style.css": {"target_path": "src/styles/base/style.css", "size": 10525, "type": "css", "category": "module_style"}, "scripts/theme.js": {"target_path": "src/scripts/theme.js", "size": 1019, "type": "js", "category": "module"}, "server/dataStorage.js": {"target_path": "src/scripts/dataStorage.js", "size": 6407, "type": "js", "category": "module"}, "server/routes/subtasks.js": {"target_path": "src/scripts/subtasks.js", "size": 2736, "type": "js", "category": "module"}, "server/timetableHandler.js": {"target_path": "src/scripts/timetableHandler.js", "size": 672, "type": "js", "category": "module"}, "server.js": {"target_path": "src/scripts/server.js", "size": 25361, "type": "js", "category": "module"}, "settings.html": {"target_path": "src/views/settings.html", "size": 5577, "type": "html", "category": "view"}, "sleep-saboteurs.html": {"target_path": "src/views/sleep-saboteurs.html", "size": 7732, "type": "html", "category": "view"}, "study-spaces.html": {"target_path": "src/views/study-spaces.html", "size": 21065, "type": "html", "category": "view"}, "styles/calendar.css": {"target_path": "src/styles/base/calendar.css", "size": 9141, "type": "css", "category": "module_style"}, "styles/index.css": {"target_path": "src/styles/base/index.css", "size": 414, "type": "css", "category": "module_style"}, "styles/main.css": {"target_path": "src/styles/base/main.css", "size": 15630, "type": "css", "category": "module_style"}, "styles/study-spaces.css": {"target_path": "src/styles/base/study-spaces.css", "size": 3850, "type": "css", "category": "module_style"}, "styles/tasks.css": {"target_path": "src/styles/base/tasks.css", "size": 6469, "type": "css", "category": "module_style"}, "subject-marks.html": {"target_path": "src/views/subject-marks.html", "size": 8598, "type": "html", "category": "view"}, "tasks.html": {"target_path": "src/views/tasks.html", "size": 24458, "type": "html", "category": "view"}, "test-worker.js": {"target_path": "src/scripts/test-worker.js", "size": 3455, "type": "js", "category": "module"}, "worker.js": {"target_path": "src/scripts/worker.js", "size": 6619, "type": "js", "category": "module"}, "workers/imageAnalysis.js": {"target_path": "src/scripts/imageAnalysis.js", "size": 7398, "type": "js", "category": "module"}, "workspace.html": {"target_path": "src/views/workspace.html", "size": 20606, "type": "html", "category": "view"}}}, "migration_plan": [{"step": 1, "action": "create_directory", "target": "src\\integrations", "description": "Create directory structure: src\\integrations"}, {"step": 2, "action": "move_file", "source": "js/cross-tab-sync.js", "target": "src/integrations/cross-tab-sync.js", "size": 10833, "type": "js", "description": "Move js/cross-tab-sync.js → src/integrations/cross-tab-sync.js"}, {"step": 3, "action": "move_file", "source": "js/data-sync-integration.js", "target": "src/integrations/data-sync-integration.js", "size": 2027, "type": "js", "description": "Move js/data-sync-integration.js → src/integrations/data-sync-integration.js"}, {"step": 4, "action": "move_file", "source": "js/flashcardTaskIntegration.js", "target": "src/integrations/flashcardTaskIntegration.js", "size": 27730, "type": "js", "description": "Move js/flashcardTaskIntegration.js → src/integrations/flashcardTaskIntegration.js"}, {"step": 5, "action": "move_file", "source": "js/priority-sync-fix.js", "target": "src/integrations/priority-sync-fix.js", "size": 4836, "type": "js", "description": "Move js/priority-sync-fix.js → src/integrations/priority-sync-fix.js"}, {"step": 6, "action": "move_file", "source": "js/subject-marks-integration.js", "target": "src/integrations/subject-marks-integration.js", "size": 1277, "type": "js", "description": "Move js/subject-marks-integration.js → src/integrations/subject-marks-integration.js"}, {"step": 7, "action": "move_file", "source": "js/timetableIntegration.js", "target": "src/integrations/timetableIntegration.js", "size": 1255, "type": "js", "description": "Move js/timetableIntegration.js → src/integrations/timetableIntegration.js"}, {"step": 8, "action": "move_file", "source": "js/weightage-connector.js", "target": "src/integrations/weightage-connector.js", "size": 9813, "type": "js", "description": "Move js/weightage-connector.js → src/integrations/weightage-connector.js"}, {"step": 9, "action": "move_file", "source": "js/workspaceFlashcardIntegration.js", "target": "src/integrations/workspaceFlashcardIntegration.js", "size": 25930, "type": "js", "description": "Move js/workspaceFlashcardIntegration.js → src/integrations/workspaceFlashcardIntegration.js"}, {"step": 10, "action": "create_directory", "target": "src\\scripts", "description": "Create directory structure: src\\scripts"}, {"step": 11, "action": "move_file", "source": "js/academic-details.js", "target": "src/scripts/academic-details.js", "size": 2090, "type": "js", "description": "Move js/academic-details.js → src/scripts/academic-details.js"}, {"step": 12, "action": "move_file", "source": "js/add-favicon.js", "target": "src/scripts/add-favicon.js", "size": 2888, "type": "js", "description": "Move js/add-favicon.js → src/scripts/add-favicon.js"}, {"step": 13, "action": "move_file", "source": "js/ai-latex-conversion.js", "target": "src/scripts/ai-latex-conversion.js", "size": 746, "type": "js", "description": "Move js/ai-latex-conversion.js → src/scripts/ai-latex-conversion.js"}, {"step": 14, "action": "move_file", "source": "js/alarm-handler.js", "target": "src/scripts/alarm-handler.js", "size": 2032, "type": "js", "description": "Move js/alarm-handler.js → src/scripts/alarm-handler.js"}, {"step": 15, "action": "move_file", "source": "js/data-loader.js", "target": "src/scripts/data-loader.js", "size": 0, "type": "js", "description": "Move js/data-loader.js → src/scripts/data-loader.js"}, {"step": 16, "action": "move_file", "source": "js/energyHologram.js", "target": "src/scripts/energyHologram.js", "size": 8670, "type": "js", "description": "Move js/energyHologram.js → src/scripts/energyHologram.js"}, {"step": 17, "action": "move_file", "source": "js/energyLevels.js", "target": "src/scripts/energyLevels.js", "size": 1616, "type": "js", "description": "Move js/energyLevels.js → src/scripts/energyLevels.js"}, {"step": 18, "action": "move_file", "source": "js/fileViewer.js", "target": "src/scripts/fileViewer.js", "size": 17164, "type": "js", "description": "Move js/fileViewer.js → src/scripts/fileViewer.js"}, {"step": 19, "action": "move_file", "source": "js/firebase-init.js", "target": "src/scripts/firebase-init.js", "size": 3040, "type": "js", "description": "Move js/firebase-init.js → src/scripts/firebase-init.js"}, {"step": 20, "action": "move_file", "source": "js/firestore-global.js", "target": "src/scripts/firestore-global.js", "size": 1454, "type": "js", "description": "Move js/firestore-global.js → src/scripts/firestore-global.js"}, {"step": 21, "action": "move_file", "source": "js/firestore.js", "target": "src/scripts/firestore.js", "size": 21209, "type": "js", "description": "Move js/firestore.js → src/scripts/firestore.js"}, {"step": 22, "action": "move_file", "source": "js/googleGenerativeAI.js", "target": "src/scripts/googleGenerativeAI.js", "size": 506, "type": "js", "description": "Move js/googleGenerativeAI.js → src/scripts/googleGenerativeAI.js"}, {"step": 23, "action": "move_file", "source": "js/imageAnalyzer.js", "target": "src/scripts/imageAnalyzer.js", "size": 8580, "type": "js", "description": "Move js/imageAnalyzer.js → src/scripts/imageAnalyzer.js"}, {"step": 24, "action": "move_file", "source": "js/indexedDB.js", "target": "src/scripts/indexedDB.js", "size": 0, "type": "js", "description": "Move js/indexedDB.js → src/scripts/indexedDB.js"}, {"step": 25, "action": "move_file", "source": "js/initFirestoreData.js", "target": "src/scripts/initFirestoreData.js", "size": 11030, "type": "js", "description": "Move js/initFirestoreData.js → src/scripts/initFirestoreData.js"}, {"step": 26, "action": "move_file", "source": "js/inject-header.js", "target": "src/scripts/inject-header.js", "size": 2290, "type": "js", "description": "Move js/inject-header.js → src/scripts/inject-header.js"}, {"step": 27, "action": "move_file", "source": "js/markdown-converter.js", "target": "src/scripts/markdown-converter.js", "size": 11518, "type": "js", "description": "Move js/markdown-converter.js → src/scripts/markdown-converter.js"}, {"step": 28, "action": "move_file", "source": "js/marks-tracking.js", "target": "src/scripts/marks-tracking.js", "size": 0, "type": "js", "description": "Move js/marks-tracking.js → src/scripts/marks-tracking.js"}, {"step": 29, "action": "move_file", "source": "js/pandoc-fallback.js", "target": "src/scripts/pandoc-fallback.js", "size": 2505, "type": "js", "description": "Move js/pandoc-fallback.js → src/scripts/pandoc-fallback.js"}, {"step": 30, "action": "move_file", "source": "js/pomodoroGlobal.js", "target": "src/scripts/pomodoroGlobal.js", "size": 9682, "type": "js", "description": "Move js/pomodoroGlobal.js → src/scripts/pomodoroGlobal.js"}, {"step": 31, "action": "move_file", "source": "js/priority-list-sorting.js", "target": "src/scripts/priority-list-sorting.js", "size": 10551, "type": "js", "description": "Move js/priority-list-sorting.js → src/scripts/priority-list-sorting.js"}, {"step": 32, "action": "move_file", "source": "js/priority-worker-wrapper.js", "target": "src/scripts/priority-worker-wrapper.js", "size": 3578, "type": "js", "description": "Move js/priority-worker-wrapper.js → src/scripts/priority-worker-wrapper.js"}, {"step": 33, "action": "move_file", "source": "js/reorganize-scripts.js", "target": "src/scripts/reorganize-scripts.js", "size": 4478, "type": "js", "description": "Move js/reorganize-scripts.js → src/scripts/reorganize-scripts.js"}, {"step": 34, "action": "move_file", "source": "js/sideDrawer.js", "target": "src/scripts/sideDrawer.js", "size": 7658, "type": "js", "description": "Move js/sideDrawer.js → src/scripts/sideDrawer.js"}, {"step": 35, "action": "move_file", "source": "js/simulation-enhancer.js", "target": "src/scripts/simulation-enhancer.js", "size": 20114, "type": "js", "description": "Move js/simulation-enhancer.js → src/scripts/simulation-enhancer.js"}, {"step": 36, "action": "move_file", "source": "js/sleep-saboteurs-init.js", "target": "src/scripts/sleep-saboteurs-init.js", "size": 778, "type": "js", "description": "Move js/sleep-saboteurs-init.js → src/scripts/sleep-saboteurs-init.js"}, {"step": 37, "action": "move_file", "source": "js/sleepTimeCalculator.js", "target": "src/scripts/sleepTimeCalculator.js", "size": 2346, "type": "js", "description": "Move js/sleepTimeCalculator.js → src/scripts/sleepTimeCalculator.js"}, {"step": 38, "action": "move_file", "source": "js/sm2.js", "target": "src/scripts/sm2.js", "size": 4254, "type": "js", "description": "Move js/sm2.js → src/scripts/sm2.js"}, {"step": 39, "action": "move_file", "source": "js/speech-synthesis.js", "target": "src/scripts/speech-synthesis.js", "size": 26156, "type": "js", "description": "Move js/speech-synthesis.js → src/scripts/speech-synthesis.js"}, {"step": 40, "action": "move_file", "source": "js/studySpaceAnalyzer.js", "target": "src/scripts/studySpaceAnalyzer.js", "size": 4485, "type": "js", "description": "Move js/studySpaceAnalyzer.js → src/scripts/studySpaceAnalyzer.js"}, {"step": 41, "action": "move_file", "source": "js/studySpacesFirestore.js", "target": "src/scripts/studySpacesFirestore.js", "size": 8971, "type": "js", "description": "Move js/studySpacesFirestore.js → src/scripts/studySpacesFirestore.js"}, {"step": 42, "action": "move_file", "source": "js/subject-management.js", "target": "src/scripts/subject-management.js", "size": 23637, "type": "js", "description": "Move js/subject-management.js → src/scripts/subject-management.js"}, {"step": 43, "action": "move_file", "source": "js/subject-marks.js", "target": "src/scripts/subject-marks.js", "size": 12652, "type": "js", "description": "Move js/subject-marks.js → src/scripts/subject-marks.js"}, {"step": 44, "action": "move_file", "source": "js/task-notes-injector.js", "target": "src/scripts/task-notes-injector.js", "size": 6313, "type": "js", "description": "Move js/task-notes-injector.js → src/scripts/task-notes-injector.js"}, {"step": 45, "action": "move_file", "source": "js/task-notes.js", "target": "src/scripts/task-notes.js", "size": 25499, "type": "js", "description": "Move js/task-notes.js → src/scripts/task-notes.js"}, {"step": 46, "action": "move_file", "source": "js/taskAttachments.js", "target": "src/scripts/taskAttachments.js", "size": 26806, "type": "js", "description": "Move js/taskAttachments.js → src/scripts/taskAttachments.js"}, {"step": 47, "action": "move_file", "source": "js/taskFilters.js", "target": "src/scripts/taskFilters.js", "size": 6515, "type": "js", "description": "Move js/taskFilters.js → src/scripts/taskFilters.js"}, {"step": 48, "action": "move_file", "source": "js/taskLinks.js", "target": "src/scripts/taskLinks.js", "size": 23567, "type": "js", "description": "Move js/taskLinks.js → src/scripts/taskLinks.js"}, {"step": 49, "action": "move_file", "source": "js/text-expansion.js", "target": "src/scripts/text-expansion.js", "size": 26396, "type": "js", "description": "Move js/text-expansion.js → src/scripts/text-expansion.js"}, {"step": 50, "action": "move_file", "source": "js/timetableAnalyzer.js", "target": "src/scripts/timetableAnalyzer.js", "size": 8930, "type": "js", "description": "Move js/timetableAnalyzer.js → src/scripts/timetableAnalyzer.js"}, {"step": 51, "action": "move_file", "source": "js/update-html-files.js", "target": "src/scripts/update-html-files.js", "size": 1614, "type": "js", "description": "Move js/update-html-files.js → src/scripts/update-html-files.js"}, {"step": 52, "action": "move_file", "source": "js/workspace-attachments.js", "target": "src/scripts/workspace-attachments.js", "size": 24543, "type": "js", "description": "Move js/workspace-attachments.js → src/scripts/workspace-attachments.js"}, {"step": 53, "action": "move_file", "source": "js/workspace-core.js", "target": "src/scripts/workspace-core.js", "size": 12221, "type": "js", "description": "Move js/workspace-core.js → src/scripts/workspace-core.js"}, {"step": 54, "action": "move_file", "source": "js/workspace-document.js", "target": "src/scripts/workspace-document.js", "size": 14191, "type": "js", "description": "Move js/workspace-document.js → src/scripts/workspace-document.js"}, {"step": 55, "action": "move_file", "source": "js/workspace-formatting.js", "target": "src/scripts/workspace-formatting.js", "size": 5203, "type": "js", "description": "Move js/workspace-formatting.js → src/scripts/workspace-formatting.js"}, {"step": 56, "action": "move_file", "source": "js/workspace-media.js", "target": "src/scripts/workspace-media.js", "size": 9721, "type": "js", "description": "Move js/workspace-media.js → src/scripts/workspace-media.js"}, {"step": 57, "action": "move_file", "source": "js/workspace-tables-links.js", "target": "src/scripts/workspace-tables-links.js", "size": 10969, "type": "js", "description": "Move js/workspace-tables-links.js → src/scripts/workspace-tables-links.js"}, {"step": 58, "action": "move_file", "source": "priority-calculator-with-worker.js", "target": "src/scripts/priority-calculator-with-worker.js", "size": 25335, "type": "js", "description": "Move priority-calculator-with-worker.js → src/scripts/priority-calculator-with-worker.js"}, {"step": 59, "action": "move_file", "source": "priority-calculator.js", "target": "src/scripts/priority-calculator.js", "size": 26330, "type": "js", "description": "Move priority-calculator.js → src/scripts/priority-calculator.js"}, {"step": 60, "action": "move_file", "source": "relaxed-mode/script.js", "target": "src/scripts/script.js", "size": 11649, "type": "js", "description": "Move relaxed-mode/script.js → src/scripts/script.js"}, {"step": 61, "action": "move_file", "source": "scripts/theme.js", "target": "src/scripts/theme.js", "size": 1019, "type": "js", "description": "Move scripts/theme.js → src/scripts/theme.js"}, {"step": 62, "action": "move_file", "source": "server/dataStorage.js", "target": "src/scripts/dataStorage.js", "size": 6407, "type": "js", "description": "Move server/dataStorage.js → src/scripts/dataStorage.js"}, {"step": 63, "action": "move_file", "source": "server/routes/subtasks.js", "target": "src/scripts/subtasks.js", "size": 2736, "type": "js", "description": "Move server/routes/subtasks.js → src/scripts/subtasks.js"}, {"step": 64, "action": "move_file", "source": "server/timetableHandler.js", "target": "src/scripts/timetableHandler.js", "size": 672, "type": "js", "description": "Move server/timetableHandler.js → src/scripts/timetableHandler.js"}, {"step": 65, "action": "move_file", "source": "server.js", "target": "src/scripts/server.js", "size": 25361, "type": "js", "description": "Move server.js → src/scripts/server.js"}, {"step": 66, "action": "move_file", "source": "test-worker.js", "target": "src/scripts/test-worker.js", "size": 3455, "type": "js", "description": "Move test-worker.js → src/scripts/test-worker.js"}, {"step": 67, "action": "move_file", "source": "worker.js", "target": "src/scripts/worker.js", "size": 6619, "type": "js", "description": "Move worker.js → src/scripts/worker.js"}, {"step": 68, "action": "move_file", "source": "workers/imageAnalysis.js", "target": "src/scripts/imageAnalysis.js", "size": 7398, "type": "js", "description": "Move workers/imageAnalysis.js → src/scripts/imageAnalysis.js"}, {"step": 69, "action": "create_directory", "target": "src\\scripts\\components", "description": "Create directory structure: src\\scripts\\components"}, {"step": 70, "action": "move_file", "source": "js/alarm-mini-display.js", "target": "src/scripts/components/alarm-mini-display.js", "size": 3702, "type": "js", "description": "Move js/alarm-mini-display.js → src/scripts/components/alarm-mini-display.js"}, {"step": 71, "action": "move_file", "source": "js/clock-display.js", "target": "src/scripts/components/clock-display.js", "size": 2038, "type": "js", "description": "Move js/clock-display.js → src/scripts/components/clock-display.js"}, {"step": 72, "action": "move_file", "source": "js/subject-marks-ui.js", "target": "src/scripts/components/subject-marks-ui.js", "size": 25657, "type": "js", "description": "Move js/subject-marks-ui.js → src/scripts/components/subject-marks-ui.js"}, {"step": 73, "action": "move_file", "source": "js/ui-utilities.js", "target": "src/scripts/components/ui-utilities.js", "size": 5298, "type": "js", "description": "Move js/ui-utilities.js → src/scripts/components/ui-utilities.js"}, {"step": 74, "action": "move_file", "source": "js/userGuidance.js", "target": "src/scripts/components/userGuidance.js", "size": 20953, "type": "js", "description": "Move js/userGuidance.js → src/scripts/components/userGuidance.js"}, {"step": 75, "action": "move_file", "source": "js/workspace-ui.js", "target": "src/scripts/components/workspace-ui.js", "size": 4594, "type": "js", "description": "Move js/workspace-ui.js → src/scripts/components/workspace-ui.js"}, {"step": 76, "action": "create_directory", "target": "src\\scripts\\views", "description": "Create directory structure: src\\scripts\\views"}, {"step": 77, "action": "move_file", "source": "js/ai-researcher.js", "target": "src/scripts/views/ai-researcher.js", "size": 101375, "type": "js", "description": "Move js/ai-researcher.js → src/scripts/views/ai-researcher.js"}, {"step": 78, "action": "move_file", "source": "js/calendar-views.js", "target": "src/scripts/views/calendar-views.js", "size": 33294, "type": "js", "description": "Move js/calendar-views.js → src/scripts/views/calendar-views.js"}, {"step": 79, "action": "move_file", "source": "js/calendarManager.js", "target": "src/scripts/views/calendarManager.js", "size": 43345, "type": "js", "description": "Move js/calendarManager.js → src/scripts/views/calendarManager.js"}, {"step": 80, "action": "move_file", "source": "js/flashcardManager.js", "target": "src/scripts/views/flashcardManager.js", "size": 49955, "type": "js", "description": "Move js/flashcardManager.js → src/scripts/views/flashcardManager.js"}, {"step": 81, "action": "move_file", "source": "js/flashcards.js", "target": "src/scripts/views/flashcards.js", "size": 51674, "type": "js", "description": "Move js/flashcards.js → src/scripts/views/flashcards.js"}, {"step": 82, "action": "move_file", "source": "js/googleDriveApi.js", "target": "src/scripts/views/googleDriveApi.js", "size": 72304, "type": "js", "description": "Move js/googleDriveApi.js → src/scripts/views/googleDriveApi.js"}, {"step": 83, "action": "move_file", "source": "js/grind-speech-synthesis.js", "target": "src/scripts/views/grind-speech-synthesis.js", "size": 34139, "type": "js", "description": "Move js/grind-speech-synthesis.js → src/scripts/views/grind-speech-synthesis.js"}, {"step": 84, "action": "move_file", "source": "js/pomodoroTimer.js", "target": "src/scripts/views/pomodoroTimer.js", "size": 34711, "type": "js", "description": "Move js/pomodoroTimer.js → src/scripts/views/pomodoroTimer.js"}, {"step": 85, "action": "move_file", "source": "js/semester-management.js", "target": "src/scripts/views/semester-management.js", "size": 62190, "type": "js", "description": "Move js/semester-management.js → src/scripts/views/semester-management.js"}, {"step": 86, "action": "move_file", "source": "js/speech-recognition.js", "target": "src/scripts/views/speech-recognition.js", "size": 81174, "type": "js", "description": "Move js/speech-recognition.js → src/scripts/views/speech-recognition.js"}, {"step": 87, "action": "move_file", "source": "js/studySpacesManager.js", "target": "src/scripts/views/studySpacesManager.js", "size": 57162, "type": "js", "description": "Move js/studySpacesManager.js → src/scripts/views/studySpacesManager.js"}, {"step": 88, "action": "move_file", "source": "js/test-feedback.js", "target": "src/scripts/views/test-feedback.js", "size": 53354, "type": "js", "description": "Move js/test-feedback.js → src/scripts/views/test-feedback.js"}, {"step": 89, "action": "move_file", "source": "js/todoistIntegration.js", "target": "src/scripts/views/todoistIntegration.js", "size": 30152, "type": "js", "description": "Move js/todoistIntegration.js → src/scripts/views/todoistIntegration.js"}, {"step": 90, "action": "create_directory", "target": "src\\services", "description": "Create directory structure: src\\services"}, {"step": 91, "action": "move_file", "source": "js/alarm-data-service.js", "target": "src/services/alarm-data-service.js", "size": 9780, "type": "js", "description": "Move js/alarm-data-service.js → src/services/alarm-data-service.js"}, {"step": 92, "action": "move_file", "source": "js/alarm-service-worker.js", "target": "src/services/alarm-service-worker.js", "size": 4761, "type": "js", "description": "Move js/alarm-service-worker.js → src/services/alarm-service-worker.js"}, {"step": 93, "action": "move_file", "source": "js/alarm-service.js", "target": "src/services/alarm-service.js", "size": 20505, "type": "js", "description": "Move js/alarm-service.js → src/services/alarm-service.js"}, {"step": 94, "action": "move_file", "source": "js/api-optimization.js", "target": "src/services/api-optimization.js", "size": 17304, "type": "js", "description": "Move js/api-optimization.js → src/services/api-optimization.js"}, {"step": 95, "action": "move_file", "source": "js/api-settings.js", "target": "src/services/api-settings.js", "size": 6746, "type": "js", "description": "Move js/api-settings.js → src/services/api-settings.js"}, {"step": 96, "action": "move_file", "source": "js/apiSettingsManager.js", "target": "src/services/apiSettingsManager.js", "size": 5021, "type": "js", "description": "Move js/apiSettingsManager.js → src/services/apiSettingsManager.js"}, {"step": 97, "action": "move_file", "source": "js/auth.js", "target": "src/services/auth.js", "size": 11414, "type": "js", "description": "Move js/auth.js → src/services/auth.js"}, {"step": 98, "action": "move_file", "source": "js/currentTaskManager.js", "target": "src/services/currentTaskManager.js", "size": 11858, "type": "js", "description": "Move js/currentTaskManager.js → src/services/currentTaskManager.js"}, {"step": 99, "action": "move_file", "source": "js/data-sync-manager.js", "target": "src/services/data-sync-manager.js", "size": 6643, "type": "js", "description": "Move js/data-sync-manager.js → src/services/data-sync-manager.js"}, {"step": 100, "action": "move_file", "source": "js/firebase-config.js", "target": "src/services/firebase-config.js", "size": 3200, "type": "js", "description": "Move js/firebase-config.js → src/services/firebase-config.js"}, {"step": 101, "action": "move_file", "source": "js/firebaseAuth.js", "target": "src/services/firebaseAuth.js", "size": 2385, "type": "js", "description": "Move js/firebaseAuth.js → src/services/firebaseAuth.js"}, {"step": 102, "action": "move_file", "source": "js/firebaseConfig.js", "target": "src/services/firebaseConfig.js", "size": 1398, "type": "js", "description": "Move js/firebaseConfig.js → src/services/firebaseConfig.js"}, {"step": 103, "action": "move_file", "source": "js/gemini-api.js", "target": "src/services/gemini-api.js", "size": 4024, "type": "js", "description": "Move js/gemini-api.js → src/services/gemini-api.js"}, {"step": 104, "action": "move_file", "source": "js/quoteManager.js", "target": "src/services/quoteManager.js", "size": 4317, "type": "js", "description": "Move js/quoteManager.js → src/services/quoteManager.js"}, {"step": 105, "action": "move_file", "source": "js/recipeManager.js", "target": "src/services/recipeManager.js", "size": 19704, "type": "js", "description": "Move js/recipeManager.js → src/services/recipeManager.js"}, {"step": 106, "action": "move_file", "source": "js/roleModelManager.js", "target": "src/services/roleModelManager.js", "size": 10829, "type": "js", "description": "Move js/roleModelManager.js → src/services/roleModelManager.js"}, {"step": 107, "action": "move_file", "source": "js/scheduleManager.js", "target": "src/services/scheduleManager.js", "size": 5023, "type": "js", "description": "Move js/scheduleManager.js → src/services/scheduleManager.js"}, {"step": 108, "action": "move_file", "source": "js/sleepScheduleManager.js", "target": "src/services/sleepScheduleManager.js", "size": 2568, "type": "js", "description": "Move js/sleepScheduleManager.js → src/services/sleepScheduleManager.js"}, {"step": 109, "action": "move_file", "source": "js/soundManager.js", "target": "src/services/soundManager.js", "size": 3372, "type": "js", "description": "Move js/soundManager.js → src/services/soundManager.js"}, {"step": 110, "action": "move_file", "source": "js/storageManager.js", "target": "src/services/storageManager.js", "size": 2014, "type": "js", "description": "Move js/storageManager.js → src/services/storageManager.js"}, {"step": 111, "action": "move_file", "source": "js/tasksManager.js", "target": "src/services/tasksManager.js", "size": 8706, "type": "js", "description": "Move js/tasksManager.js → src/services/tasksManager.js"}, {"step": 112, "action": "move_file", "source": "js/theme-manager.js", "target": "src/services/theme-manager.js", "size": 1830, "type": "js", "description": "Move js/theme-manager.js → src/services/theme-manager.js"}, {"step": 113, "action": "move_file", "source": "js/themeManager.js", "target": "src/services/themeManager.js", "size": 3094, "type": "js", "description": "Move js/themeManager.js → src/services/themeManager.js"}, {"step": 114, "action": "move_file", "source": "js/transitionManager.js", "target": "src/services/transitionManager.js", "size": 3222, "type": "js", "description": "Move js/transitionManager.js → src/services/transitionManager.js"}, {"step": 115, "action": "move_file", "source": "public/js/cacheManager.js", "target": "src/services/cacheManager.js", "size": 3212, "type": "js", "description": "Move public/js/cacheManager.js → src/services/cacheManager.js"}, {"step": 116, "action": "move_file", "source": "public/service-worker.js", "target": "src/services/service-worker.js", "size": 1, "type": "js", "description": "Move public/service-worker.js → src/services/service-worker.js"}, {"step": 117, "action": "create_directory", "target": "src\\styles\\base", "description": "Create directory structure: src\\styles\\base"}, {"step": 118, "action": "move_file", "source": "css/ai-search-response.css", "target": "src/styles/base/ai-search-response.css", "size": 12338, "type": "css", "description": "Move css/ai-search-response.css → src/styles/base/ai-search-response.css"}, {"step": 119, "action": "move_file", "source": "css/alarm-service.css", "target": "src/styles/base/alarm-service.css", "size": 11193, "type": "css", "description": "Move css/alarm-service.css → src/styles/base/alarm-service.css"}, {"step": 120, "action": "move_file", "source": "css/compact-style.css", "target": "src/styles/base/compact-style.css", "size": 4220, "type": "css", "description": "Move css/compact-style.css → src/styles/base/compact-style.css"}, {"step": 121, "action": "move_file", "source": "css/flashcards.css", "target": "src/styles/base/flashcards.css", "size": 5129, "type": "css", "description": "Move css/flashcards.css → src/styles/base/flashcards.css"}, {"step": 122, "action": "move_file", "source": "css/notification.css", "target": "src/styles/base/notification.css", "size": 967, "type": "css", "description": "Move css/notification.css → src/styles/base/notification.css"}, {"step": 123, "action": "move_file", "source": "css/priority-calculator.css", "target": "src/styles/base/priority-calculator.css", "size": 7623, "type": "css", "description": "Move css/priority-calculator.css → src/styles/base/priority-calculator.css"}, {"step": 124, "action": "move_file", "source": "css/priority-list.css", "target": "src/styles/base/priority-list.css", "size": 10003, "type": "css", "description": "Move css/priority-list.css → src/styles/base/priority-list.css"}, {"step": 125, "action": "move_file", "source": "css/settings.css", "target": "src/styles/base/settings.css", "size": 7412, "type": "css", "description": "Move css/settings.css → src/styles/base/settings.css"}, {"step": 126, "action": "move_file", "source": "css/sideDrawer.css", "target": "src/styles/base/sideDrawer.css", "size": 15934, "type": "css", "description": "Move css/sideDrawer.css → src/styles/base/sideDrawer.css"}, {"step": 127, "action": "move_file", "source": "css/simulation-enhancer.css", "target": "src/styles/base/simulation-enhancer.css", "size": 4281, "type": "css", "description": "Move css/simulation-enhancer.css → src/styles/base/simulation-enhancer.css"}, {"step": 128, "action": "move_file", "source": "css/sleep-saboteurs.css", "target": "src/styles/base/sleep-saboteurs.css", "size": 17245, "type": "css", "description": "Move css/sleep-saboteurs.css → src/styles/base/sleep-saboteurs.css"}, {"step": 129, "action": "move_file", "source": "css/study-spaces.css", "target": "src/styles/base/study-spaces.css", "size": 19318, "type": "css", "description": "Move css/study-spaces.css → src/styles/base/study-spaces.css"}, {"step": 130, "action": "move_file", "source": "css/subject-marks.css", "target": "src/styles/base/subject-marks.css", "size": 4989, "type": "css", "description": "Move css/subject-marks.css → src/styles/base/subject-marks.css"}, {"step": 131, "action": "move_file", "source": "css/task-display.css", "target": "src/styles/base/task-display.css", "size": 828, "type": "css", "description": "Move css/task-display.css → src/styles/base/task-display.css"}, {"step": 132, "action": "move_file", "source": "css/task-notes.css", "target": "src/styles/base/task-notes.css", "size": 9506, "type": "css", "description": "Move css/task-notes.css → src/styles/base/task-notes.css"}, {"step": 133, "action": "move_file", "source": "css/taskLinks.css", "target": "src/styles/base/taskLinks.css", "size": 9748, "type": "css", "description": "Move css/taskLinks.css → src/styles/base/taskLinks.css"}, {"step": 134, "action": "move_file", "source": "css/test-feedback.css", "target": "src/styles/base/test-feedback.css", "size": 16264, "type": "css", "description": "Move css/test-feedback.css → src/styles/base/test-feedback.css"}, {"step": 135, "action": "move_file", "source": "css/text-expansion.css", "target": "src/styles/base/text-expansion.css", "size": 6288, "type": "css", "description": "Move css/text-expansion.css → src/styles/base/text-expansion.css"}, {"step": 136, "action": "move_file", "source": "relaxed-mode/style.css", "target": "src/styles/base/style.css", "size": 10525, "type": "css", "description": "Move relaxed-mode/style.css → src/styles/base/style.css"}, {"step": 137, "action": "move_file", "source": "styles/calendar.css", "target": "src/styles/base/calendar.css", "size": 9141, "type": "css", "description": "Move styles/calendar.css → src/styles/base/calendar.css"}, {"step": 138, "action": "move_file", "source": "styles/index.css", "target": "src/styles/base/index.css", "size": 414, "type": "css", "description": "Move styles/index.css → src/styles/base/index.css"}, {"step": 139, "action": "move_file", "source": "styles/main.css", "target": "src/styles/base/main.css", "size": 15630, "type": "css", "description": "Move styles/main.css → src/styles/base/main.css"}, {"step": 140, "action": "move_file", "source": "styles/study-spaces.css", "target": "src/styles/base/study-spaces.css", "size": 3850, "type": "css", "description": "Move styles/study-spaces.css → src/styles/base/study-spaces.css"}, {"step": 141, "action": "move_file", "source": "styles/tasks.css", "target": "src/styles/base/tasks.css", "size": 6469, "type": "css", "description": "Move styles/tasks.css → src/styles/base/tasks.css"}, {"step": 142, "action": "create_directory", "target": "src\\styles\\views", "description": "Create directory structure: src\\styles\\views"}, {"step": 143, "action": "move_file", "source": "css/academic-details.css", "target": "src/styles/views/academic-details.css", "size": 27044, "type": "css", "description": "Move css/academic-details.css → src/styles/views/academic-details.css"}, {"step": 144, "action": "move_file", "source": "css/daily-calendar.css", "target": "src/styles/views/daily-calendar.css", "size": 50181, "type": "css", "description": "Move css/daily-calendar.css → src/styles/views/daily-calendar.css"}, {"step": 145, "action": "move_file", "source": "css/extracted.css", "target": "src/styles/views/extracted.css", "size": 39627, "type": "css", "description": "Move css/extracted.css → src/styles/views/extracted.css"}, {"step": 146, "action": "move_file", "source": "css/workspace.css", "target": "src/styles/views/workspace.css", "size": 40046, "type": "css", "description": "Move css/workspace.css → src/styles/views/workspace.css"}, {"step": 147, "action": "move_file", "source": "grind.css", "target": "src/styles/views/grind.css", "size": 125643, "type": "css", "description": "Move grind.css → src/styles/views/grind.css"}, {"step": 148, "action": "create_directory", "target": "src\\utils", "description": "Create directory structure: src\\utils"}, {"step": 149, "action": "move_file", "source": "js/common-header.js", "target": "src/utils/common-header.js", "size": 5780, "type": "js", "description": "Move js/common-header.js → src/utils/common-header.js"}, {"step": 150, "action": "move_file", "source": "js/common.js", "target": "src/utils/common.js", "size": 1758, "type": "js", "description": "Move js/common.js → src/utils/common.js"}, {"step": 151, "action": "move_file", "source": "js/priority-list-utils.js", "target": "src/utils/priority-list-utils.js", "size": 26792, "type": "js", "description": "Move js/priority-list-utils.js → src/utils/priority-list-utils.js"}, {"step": 152, "action": "create_directory", "target": "src\\views", "description": "Create directory structure: src\\views"}, {"step": 153, "action": "move_file", "source": "404.html", "target": "src/views/404.html", "size": 2114, "type": "html", "description": "Move 404.html → src/views/404.html"}, {"step": 154, "action": "move_file", "source": "academic-details.html", "target": "src/views/academic-details.html", "size": 26146, "type": "html", "description": "Move academic-details.html → src/views/academic-details.html"}, {"step": 155, "action": "move_file", "source": "daily-calendar.html", "target": "src/views/daily-calendar.html", "size": 10240, "type": "html", "description": "Move daily-calendar.html → src/views/daily-calendar.html"}, {"step": 156, "action": "move_file", "source": "extracted.html", "target": "src/views/extracted.html", "size": 92348, "type": "html", "description": "Move extracted.html → src/views/extracted.html"}, {"step": 157, "action": "move_file", "source": "flashcards.html", "target": "src/views/flashcards.html", "size": 16728, "type": "html", "description": "Move flashcards.html → src/views/flashcards.html"}, {"step": 158, "action": "move_file", "source": "grind.html", "target": "src/views/grind.html", "size": 201061, "type": "html", "description": "Move grind.html → src/views/grind.html"}, {"step": 159, "action": "move_file", "source": "index.html", "target": "src/views/index.html", "size": 2464, "type": "html", "description": "Move index.html → src/views/index.html"}, {"step": 160, "action": "move_file", "source": "instant-test-feedback.html", "target": "src/views/instant-test-feedback.html", "size": 23222, "type": "html", "description": "Move instant-test-feedback.html → src/views/instant-test-feedback.html"}, {"step": 161, "action": "move_file", "source": "landing.html", "target": "src/views/landing.html", "size": 55202, "type": "html", "description": "Move landing.html → src/views/landing.html"}, {"step": 162, "action": "move_file", "source": "priority-calculator.html", "target": "src/views/priority-calculator.html", "size": 7020, "type": "html", "description": "Move priority-calculator.html → src/views/priority-calculator.html"}, {"step": 163, "action": "move_file", "source": "priority-list.html", "target": "src/views/priority-list.html", "size": 5227, "type": "html", "description": "Move priority-list.html → src/views/priority-list.html"}, {"step": 164, "action": "move_file", "source": "relaxed-mode/index.html", "target": "src/views/index.html", "size": 7111, "type": "html", "description": "Move relaxed-mode/index.html → src/views/index.html"}, {"step": 165, "action": "move_file", "source": "settings.html", "target": "src/views/settings.html", "size": 5577, "type": "html", "description": "Move settings.html → src/views/settings.html"}, {"step": 166, "action": "move_file", "source": "sleep-saboteurs.html", "target": "src/views/sleep-saboteurs.html", "size": 7732, "type": "html", "description": "Move sleep-saboteurs.html → src/views/sleep-saboteurs.html"}, {"step": 167, "action": "move_file", "source": "study-spaces.html", "target": "src/views/study-spaces.html", "size": 21065, "type": "html", "description": "Move study-spaces.html → src/views/study-spaces.html"}, {"step": 168, "action": "move_file", "source": "subject-marks.html", "target": "src/views/subject-marks.html", "size": 8598, "type": "html", "description": "Move subject-marks.html → src/views/subject-marks.html"}, {"step": 169, "action": "move_file", "source": "tasks.html", "target": "src/views/tasks.html", "size": 24458, "type": "html", "description": "Move tasks.html → src/views/tasks.html"}, {"step": 170, "action": "move_file", "source": "workspace.html", "target": "src/views/workspace.html", "size": 20606, "type": "html", "description": "Move workspace.html → src/views/workspace.html"}]}