# Dependency Mapper Agent

A comprehensive tool for extracting and mapping `<script>` and `<link>` tag references from HTML files in web development projects.

## Overview

The Dependency Mapper Agent provides multiple approaches to scan and analyze web dependencies:

1. **Bash Script** (`extract_dependencies.sh`) - One-liner approach using grep and sed
2. **Python Implementation** (`dependency_mapper.py`) - Full-featured dependency analyzer
3. **Simple Parser** (`parse_dependencies.py`) - Processes extracted dependency files
4. **Demo Scripts** - Various demonstration implementations

## Features

### Bash Implementation (`extract_dependencies.sh`)

- **One-liner Extraction**: Uses grep with regex patterns to extract dependencies
- **Multiple Pattern Support**: Handles various script and link tag formats
- **Automatic Categorization**: Separates external vs local dependencies
- **Quick Analysis**: Provides immediate summary statistics
- **Simple Output**: Creates `js_deps.txt` and `css_deps.txt` files

### Python Implementation (`dependency_mapper.py`)

- **Comprehensive Scanning**: Recursively scans all HTML files
- **Advanced Pattern Matching**: Handles complex tag structures and attributes
- **Multiple Output Formats**: JSON, CSV, and text file generation
- **Detailed Analysis**: File mapping, domain analysis, directory distribution
- **Path Normalization**: Resolves relative paths consistently
- **Error Handling**: Graceful handling of malformed HTML or inaccessible files

### Dependency Parser (`parse_dependencies.py`)

- **Basic Structure**: Implements the exact Python snippet from requirements
- **Enhanced Analysis**: Categorizes external vs local dependencies
- **Domain Analysis**: Identifies most common external domains
- **Directory Mapping**: Analyzes local file organization
- **Graph Data**: Generates visualization-ready data structures

## Usage

### Bash One-liner Approach

```bash
# Extract JavaScript dependencies
grep -RohE '<script[^>]+src="[^"]+"' . | sed -E 's/.*src="([^"]+)".*/\1/' | sort -u > js_deps.txt

# Extract CSS dependencies  
grep -RohE '<link[^>]+href="[^"]+"\s*rel="stylesheet"' . | sed -E 's/.*href="([^"]+)".*/\1/' | sort -u > css_deps.txt

# Or run the complete script
bash extract_dependencies.sh
```

### Python Implementation

```bash
# Basic usage - generates all report formats
python dependency_mapper.py

# Generate specific formats
python dependency_mapper.py --simple     # Text files only
python dependency_mapper.py --json       # JSON report only
python dependency_mapper.py --csv        # CSV report only
python dependency_mapper.py --all        # All formats

# Scan specific directory
python dependency_mapper.py --dir /path/to/project

# Quiet mode (no summary output)
python dependency_mapper.py --quiet
```

### Python Snippet (as per requirements)

```python
import json
deps = {'js': open('js_deps.txt').read().split(), 'css': open('css_deps.txt').read().split()}
print(json.dumps(deps, indent=2))
```

## Sample Results

Based on the current project analysis:

### Summary Statistics
- **Total Dependencies**: 69 dependencies found
- **JavaScript**: 32 dependencies (9 external, 23 local)
- **CSS**: 37 dependencies (6 external, 31 local)

### Top External Domains
- **cdn.jsdelivr.net**: 4 dependencies (Bootstrap, Chart.js, etc.)
- **fonts.googleapis.com**: 2 dependencies (Google Fonts)
- **cdnjs.cloudflare.com**: 2 dependencies (PDF.js, Font Awesome)
- **www.gstatic.com**: 2 dependencies (Firebase)
- **kit.fontawesome.com**: 1 dependency
- **unpkg.com**: 1 dependency

### Local Directory Distribution
- **js/**: 23 JavaScript files
- **css/**: 22 CSS files
- **styles/**: 5 CSS files
- **relaxed-mode/**: 1 CSS file
- **Root**: 2 files (grind.css, main.css)

## Output Files

### js_deps.txt
```
https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js
https://cdn.jsdelivr.net/npm/chart.js
js/ai-researcher.js
js/cross-tab-sync.js
js/pomodoroTimer.js
...
```

### css_deps.txt
```
https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css
https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap
css/sideDrawer.css
css/workspace.css
grind.css
...
```

### dependency_report.json
```json
{
  "scan_timestamp": "2024-01-15T10:30:00",
  "summary": {
    "html_files_scanned": 18,
    "total_js_dependencies": 32,
    "total_css_dependencies": 37,
    "external_dependencies": 15,
    "local_dependencies": 54
  },
  "dependencies": {
    "javascript": [...],
    "css": [...]
  },
  "file_mapping": {...}
}
```

## Supported Patterns

### Script Tags
- `<script src="path/to/file.js"></script>`
- `<script type="module" src="path/to/file.js"></script>`
- `<script defer src="path/to/file.js"></script>`
- `<script async src="path/to/file.js"></script>`
- Mixed attribute orders and quote styles

### Link Tags
- `<link rel="stylesheet" href="path/to/file.css">`
- `<link href="path/to/file.css" rel="stylesheet">`
- `<link rel="preload" href="path/to/file.css" as="style">`
- Various media queries and type attributes

## Integration Use Cases

- **Build Process Optimization**: Identify unused dependencies
- **Performance Analysis**: Track external dependency load times
- **Security Auditing**: Monitor third-party script sources
- **Bundle Analysis**: Understand dependency relationships
- **CDN Migration**: Identify candidates for CDN hosting
- **Dependency Updates**: Track version changes across files

## Requirements

### Bash Script
- Bash shell with GNU utilities
- `grep` with extended regex support (`-E`)
- `sed` with extended regex support
- `sort` and `uniq` utilities

### Python Implementation
- Python 3.6+
- Standard library modules: `re`, `json`, `csv`, `pathlib`, `datetime`, `argparse`
- No external dependencies required

## Error Handling

- **Malformed HTML**: Continues processing despite syntax errors
- **Inaccessible Files**: Logs warnings and continues
- **Missing Dependencies**: Validates input files before processing
- **Path Resolution**: Handles relative and absolute paths correctly
- **Encoding Issues**: Uses UTF-8 with error handling

## Performance Considerations

- **Large Projects**: Efficiently handles hundreds of HTML files
- **Memory Usage**: Streams file processing to minimize memory footprint
- **Regex Optimization**: Uses compiled patterns for better performance
- **Parallel Processing**: Can be extended for concurrent file processing

## Customization

The tools can be easily extended to:
- Support additional file types (e.g., PHP, JSP)
- Extract other HTML attributes (integrity, crossorigin)
- Generate custom report formats
- Integrate with build tools (Webpack, Gulp, etc.)
- Add dependency validation and health checks

## License

This Dependency Mapper Agent is provided as-is for web development dependency analysis.
