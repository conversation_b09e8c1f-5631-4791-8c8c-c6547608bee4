#!/usr/bin/env python3
"""
Manual Dependency Demo
Demonstrates the dependency extraction concept with sample data from the project.
"""

import json

def create_sample_dependencies():
    """Create sample dependency data based on observed patterns."""
    
    # Sample JavaScript dependencies extracted from the HTML files
    js_deps = [
        # External CDN dependencies
        "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js",
        "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js",
        "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js",
        "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js",
        "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js",
        "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js",
        "https://cdn.jsdelivr.net/npm/chart.js",
        "https://kit.fontawesome.com/51198d7b97.js",
        "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js",
        
        # Local JavaScript files
        "js/cross-tab-sync.js",
        "js/initFirestoreData.js", 
        "js/common.js",
        "js/userGuidance.js",
        "js/ai-researcher.js",
        "js/firebase-init.js",
        "js/inject-header.js",
        "js/storageManager.js",
        "js/grind-speech-synthesis.js",
        "js/taskLinks.js",
        "js/currentTaskManager.js",
        "js/sleepTimeCalculator.js",
        "js/energyLevels.js",
        "js/sideDrawer.js",
        "js/pomodoroTimer.js",
        "js/task-notes-injector.js",
        "js/task-notes.js",
        "js/text-expansion.js",
        "js/ai-latex-conversion.js",
        "js/cacheManager.js",
        "js/alarm-service.js",
        "js/alarm-mini-display.js",
        "priority-calculator.js"
    ]
    
    # Sample CSS dependencies extracted from the HTML files
    css_deps = [
        # External CDN stylesheets
        "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css",
        "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css",
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css",
        "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap",
        "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap",
        "https://cdn.quilljs.com/1.3.6/quill.snow.css",
        
        # Local CSS files
        "css/taskLinks.css",
        "css/search-modal.css",
        "css/sideDrawer.css",
        "css/task-display.css",
        "css/text-expansion.css",
        "css/simulation-enhancer.css",
        "css/ai-search-response.css",
        "css/task-notes.css",
        "css/alarm-service.css",
        "css/workspace.css",
        "css/extracted.css",
        "css/academic-details.css",
        "css/daily-calendar.css",
        "css/flashcards.css",
        "css/notification.css",
        "css/priority-calculator.css",
        "css/priority-list.css",
        "css/settings.css",
        "css/sleep-saboteurs.css",
        "css/study-spaces.css",
        "css/subject-marks.css",
        "css/test-feedback.css",
        "grind.css",
        "main.css",
        "styles/calendar.css",
        "styles/index.css",
        "styles/main.css",
        "styles/study-spaces.css",
        "styles/tasks.css",
        "relaxed-mode/style.css"
    ]
    
    return js_deps, css_deps

def analyze_dependencies(js_deps, css_deps):
    """Analyze the dependencies and categorize them."""
    
    # Categorize external vs local
    external_js = [dep for dep in js_deps if dep.startswith(('http://', 'https://'))]
    local_js = [dep for dep in js_deps if not dep.startswith(('http://', 'https://'))]
    
    external_css = [dep for dep in css_deps if dep.startswith(('http://', 'https://'))]
    local_css = [dep for dep in css_deps if not dep.startswith(('http://', 'https://'))]
    
    # Analyze external domains
    external_domains = {}
    for dep in external_js + external_css:
        if dep.startswith('https://cdn.jsdelivr.net'):
            external_domains['cdn.jsdelivr.net'] = external_domains.get('cdn.jsdelivr.net', 0) + 1
        elif dep.startswith('https://cdnjs.cloudflare.com'):
            external_domains['cdnjs.cloudflare.com'] = external_domains.get('cdnjs.cloudflare.com', 0) + 1
        elif dep.startswith('https://fonts.googleapis.com'):
            external_domains['fonts.googleapis.com'] = external_domains.get('fonts.googleapis.com', 0) + 1
        elif dep.startswith('https://www.gstatic.com'):
            external_domains['www.gstatic.com'] = external_domains.get('www.gstatic.com', 0) + 1
        elif dep.startswith('https://kit.fontawesome.com'):
            external_domains['kit.fontawesome.com'] = external_domains.get('kit.fontawesome.com', 0) + 1
        elif dep.startswith('https://unpkg.com'):
            external_domains['unpkg.com'] = external_domains.get('unpkg.com', 0) + 1
        elif dep.startswith('https://cdn.quilljs.com'):
            external_domains['cdn.quilljs.com'] = external_domains.get('cdn.quilljs.com', 0) + 1
    
    # Analyze local directories
    local_dirs = {}
    for dep in local_js + local_css:
        if '/' in dep:
            directory = dep.split('/')[0]
        else:
            directory = '.' # Root directory
        local_dirs[directory] = local_dirs.get(directory, 0) + 1
    
    return {
        'external_js': external_js,
        'local_js': local_js,
        'external_css': external_css,
        'local_css': local_css,
        'external_domains': external_domains,
        'local_directories': local_dirs
    }

def main():
    """Main demo function."""
    print("Dependency Mapper Agent - Manual Demo")
    print("=" * 50)
    
    # Get sample dependencies
    js_deps, css_deps = create_sample_dependencies()
    
    # Create the basic structure as specified in requirements
    deps = {
        'js': js_deps,
        'css': css_deps
    }
    
    # Write simple text files (as per original requirement)
    with open('js_deps.txt', 'w', encoding='utf-8') as f:
        for dep in sorted(js_deps):
            f.write(f"{dep}\n")
    
    with open('css_deps.txt', 'w', encoding='utf-8') as f:
        for dep in sorted(css_deps):
            f.write(f"{dep}\n")
    
    # Analyze dependencies
    analysis = analyze_dependencies(js_deps, css_deps)
    
    # Print the basic JSON structure (as per requirements)
    print("Basic dependency structure (as per requirements):")
    print(json.dumps(deps, indent=2))
    
    # Print analysis
    print(f"\n{'='*50}")
    print("DEPENDENCY ANALYSIS")
    print(f"{'='*50}")
    print(f"Total JavaScript dependencies: {len(js_deps)}")
    print(f"  External: {len(analysis['external_js'])}")
    print(f"  Local: {len(analysis['local_js'])}")
    
    print(f"\nTotal CSS dependencies: {len(css_deps)}")
    print(f"  External: {len(analysis['external_css'])}")
    print(f"  Local: {len(analysis['local_css'])}")
    
    print(f"\nExternal Domain Distribution:")
    for domain, count in sorted(analysis['external_domains'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {domain}: {count} dependencies")
    
    print(f"\nLocal Directory Distribution:")
    for directory, count in sorted(analysis['local_directories'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {directory}: {count} files")
    
    # Create comprehensive report
    comprehensive_report = {
        'summary': {
            'total_js': len(js_deps),
            'total_css': len(css_deps),
            'external_js': len(analysis['external_js']),
            'local_js': len(analysis['local_js']),
            'external_css': len(analysis['external_css']),
            'local_css': len(analysis['local_css'])
        },
        'dependencies': deps,
        'analysis': analysis,
        'external_domains': analysis['external_domains'],
        'local_directories': analysis['local_directories']
    }
    
    with open('dependency_mapping_demo.json', 'w', encoding='utf-8') as f:
        json.dump(comprehensive_report, f, indent=2, ensure_ascii=False)
    
    print(f"\nFiles generated:")
    print("- js_deps.txt (JavaScript dependencies)")
    print("- css_deps.txt (CSS dependencies)")
    print("- dependency_mapping_demo.json (Comprehensive analysis)")
    
    print(f"\nTop External Dependencies:")
    external_all = analysis['external_js'] + analysis['external_css']
    for dep in sorted(set(external_all))[:8]:
        print(f"  - {dep}")

if __name__ == "__main__":
    main()
