# File Auditor Agent

A comprehensive tool for auditing web development files (.html, .css, .js) in your project directory.

## Overview

The File Auditor Agent provides multiple ways to scan and analyze web files in your project:

1. **Python Implementation** (`file_auditor.py`) - Full-featured auditor with multiple output formats
2. **Bash Script** (`audit_files.sh`) - Simple one-liner approach for quick audits
3. **TSV Parser** (`parse_audit.py`) - Batch processing of audit results

## Features

### Python Implementation (`file_auditor.py`)

- **Comprehensive Scanning**: Recursively scans directories for .html, .css, and .js files
- **Multiple Output Formats**: 
  - TSV (Tab-Separated Values)
  - CSV (Comma-Separated Values) 
  - JSON (JavaScript Object Notation)
- **Detailed Statistics**: File counts, sizes, averages, largest/smallest files by type
- **Metadata Collection**: File paths, sizes, modification times, directory structure
- **Command-line Interface**: Flexible options for different use cases

### Bash Script (`audit_files.sh`)

- **One-liner Approach**: Uses `find` command for quick file discovery
- **TSV Output**: Creates audit.tsv with file paths and sizes
- **Quick Summary**: Shows file counts and largest files by type
- **Preview Mode**: Displays first 10 results for immediate feedback

### TSV Parser (`parse_audit.py`)

- **Batch Processing**: Converts TSV output to clean CSV format
- **Size Parsing**: Extracts numeric values from "X bytes" format
- **Statistics**: Provides total size, average, largest/smallest files
- **Error Handling**: Gracefully handles malformed data

## Usage

### Python Implementation

```bash
# Basic usage - generates all report formats
python file_auditor.py

# Generate specific formats
python file_auditor.py --tsv          # TSV only
python file_auditor.py --csv          # CSV only
python file_auditor.py --json         # JSON only
python file_auditor.py --all          # All formats

# Scan specific directory
python file_auditor.py --dir /path/to/project

# Quiet mode (no summary output)
python file_auditor.py --quiet
```

### Bash Script

```bash
# Run the audit
bash audit_files.sh

# Or make it executable and run directly
chmod +x audit_files.sh
./audit_files.sh
```

### TSV Parser

```bash
# Parse existing audit.tsv file
python parse_audit.py
```

## Output Files

### audit.tsv
Tab-separated file with relative paths and file sizes:
```
404.html	2114 bytes
academic-details.html	26146 bytes
css/academic-details.css	27044 bytes
```

### audit_report.csv
Clean CSV format for spreadsheet applications:
```csv
path,size
404.html,2114
academic-details.html,26146
css/academic-details.css,27044
```

### audit_report.json
Comprehensive JSON report with metadata and statistics:
```json
{
  "scan_timestamp": "2024-01-15T10:30:00",
  "root_directory": "/path/to/project",
  "total_files": 161,
  "summary_by_extension": {
    ".html": {
      "count": 18,
      "total_size_bytes": 536919,
      "average_size_bytes": 29828.83
    }
  },
  "files": [...]
}
```

## Sample Results

Based on the current project scan:

- **Total Files**: 161 web files found
- **HTML Files**: 18 files, 524.33 KB total
- **CSS Files**: 29 files, 480.33 KB total  
- **JS Files**: 114 files, 1576.35 KB total
- **Total Project Size**: 2.58 MB of web assets

### Largest Files by Type
- **HTML**: grind.html (201,061 bytes)
- **CSS**: grind.css (125,643 bytes)
- **JS**: ai-researcher.js (101,375 bytes)

## Requirements

### Python Implementation
- Python 3.6+
- Standard library modules: `os`, `csv`, `json`, `pathlib`, `datetime`, `argparse`

### Bash Script
- Bash shell
- `find` command with `-printf` support (GNU findutils)
- Standard Unix utilities: `grep`, `sort`, `wc`, `head`

## Integration

The File Auditor Agent can be integrated into:

- **Build Processes**: Monitor asset sizes during development
- **CI/CD Pipelines**: Track file size changes over time
- **Performance Monitoring**: Identify large files affecting load times
- **Code Reviews**: Provide file size context for changes
- **Project Documentation**: Generate asset inventories

## Error Handling

- **Permission Errors**: Gracefully handles inaccessible files
- **Malformed Data**: Continues processing despite individual file errors
- **Missing Files**: Validates input files before processing
- **Path Issues**: Resolves relative and absolute paths correctly

## Customization

The Python implementation can be easily extended to:
- Support additional file extensions
- Add custom filtering criteria
- Include additional metadata (permissions, checksums)
- Generate custom report formats
- Integrate with external tools or APIs

## License

This File Auditor Agent is provided as-is for project auditing purposes.
