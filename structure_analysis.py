#!/usr/bin/env python3
"""
Structure Analysis - Simplified Module Planner Demo
Analyzes current project and proposes target structure based on file sizes.
"""

import csv
from pathlib import Path
from collections import defaultdict


def analyze_project_structure():
    """Analyze the current project structure and propose improvements."""
    
    print("Module Planner Agent - Structure Analysis")
    print("=" * 50)
    
    # Load audit data
    try:
        with open('audit_report.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            files = list(reader)
    except FileNotFoundError:
        print("Error: audit_report.csv not found. Please run the file auditor first.")
        return
    
    # Sort by size (largest first)
    largest_files = sorted(files, key=lambda x: int(x['size']), reverse=True)
    
    print(f"Analyzed {len(files)} files")
    print("\nLargest Files (driving views vs components split):")
    print("-" * 50)
    
    # Analyze largest files to determine views vs components
    views = []
    components = []
    
    for i, file_info in enumerate(largest_files[:15]):
        path = file_info['path']
        size = int(file_info['size'])
        
        # Categorize based on size and type
        if path.endswith('.html'):
            if size > 15000:  # Large HTML files are views
                category = "VIEW"
                views.append(path)
            else:
                category = "COMPONENT"
                components.append(path)
        elif path.endswith('.js'):
            if size > 30000:  # Large JS files are view controllers
                category = "VIEW CONTROLLER"
                views.append(path)
            else:
                category = "MODULE/COMPONENT"
                components.append(path)
        elif path.endswith('.css'):
            if size > 20000:  # Large CSS files are view styles
                category = "VIEW STYLE"
                views.append(path)
            else:
                category = "COMPONENT STYLE"
                components.append(path)
        else:
            category = "OTHER"
        
        print(f"{i+1:2d}. {path:<35} ({size:>6,} bytes) → {category}")
    
    # Analyze current directory structure
    print(f"\nCurrent Directory Distribution:")
    print("-" * 30)
    
    dir_counts = defaultdict(int)
    for file_info in files:
        path = file_info['path']
        directory = str(Path(path).parent) if '/' in path else 'root'
        dir_counts[directory] += 1
    
    for directory, count in sorted(dir_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"{directory:<20}: {count:>3} files")
    
    # File type analysis
    print(f"\nFile Type Distribution:")
    print("-" * 22)
    
    type_counts = defaultdict(int)
    for file_info in files:
        path = file_info['path']
        if path.endswith('.html'):
            type_counts['HTML'] += 1
        elif path.endswith('.css'):
            type_counts['CSS'] += 1
        elif path.endswith('.js'):
            type_counts['JavaScript'] += 1
        else:
            type_counts['Other'] += 1
    
    for file_type, count in sorted(type_counts.items()):
        print(f"{file_type:<12}: {count:>3} files")
    
    return views, components, largest_files


def generate_proposed_structure():
    """Generate the proposed target structure."""
    
    structure = """
PROPOSED TARGET STRUCTURE
=========================

/src
  /components          # Reusable UI components (<15KB HTML, <30KB JS)
    /ui                # Basic UI elements (buttons, inputs, etc.)
      - sideDrawer.js/css
      - clock-display.js
      - ui-utilities.js
    /forms             # Form components
      - task-notes.js/css
      - text-expansion.js/css
    /modals            # Modal dialogs
      - notification.css
      - compact-style.css
    /widgets           # Interactive widgets
      - energyLevels.js
      - quoteManager.js
      - soundManager.js

  /views               # Main application views/pages (>15KB HTML, >30KB JS)
    /pages             # Full page views
      - grind.html/js/css (201KB HTML, large JS)
      - extracted.html (92KB)
      - landing.html (55KB)
      - workspace.html (21KB)
      - academic-details.html (26KB)
    /controllers       # View controllers (>30KB JS files)
      - ai-researcher.js (101KB)
      - googleDriveApi.js (72KB)
      - speech-recognition.js (81KB)
      - semester-management.js (62KB)
      - studySpacesManager.js (57KB)
      - test-feedback.js (53KB)
      - flashcards.js (52KB)
      - flashcardManager.js (50KB)

  /services            # Business logic and API services
    /api               # API communication
      - gemini-api.js
      - googleGenerativeAI.js
      - api-optimization.js
      - api-settings.js
    /auth              # Authentication
      - auth.js
      - firebaseAuth.js
      - firebase-config.js
    /data              # Data management
      - firestore.js
      - storageManager.js
      - data-sync-manager.js
      - indexedDB.js
    /integrations      # External services
      - todoistIntegration.js
      - cross-tab-sync.js
      - data-sync-integration.js

  /utils               # Utility functions and helpers
    /helpers           # General helpers
      - common.js
      - ui-utilities.js
      - transitionManager.js
    /managers          # Specialized managers
      - calendarManager.js
      - tasksManager.js
      - scheduleManager.js
      - themeManager.js

  /styles              # Organized stylesheets
    /base              # Base styles and variables
      - main.css
      - styles/main.css
    /views             # View-specific styles (>20KB CSS)
      - grind.css (126KB)
      - daily-calendar.css (50KB)
      - extracted.css (40KB)
      - workspace.css (40KB)
    /components        # Component styles
      - sideDrawer.css
      - task-display.css
      - taskLinks.css
      - notification.css
    /layouts           # Layout styles
      - academic-details.css
      - study-spaces.css
      - sleep-saboteurs.css

  /assets              # Static assets
    /images            # Image files
    /icons             # Icon files  
    /sounds            # Audio files
      - notification.mp3
      - pop.mp3
    /fonts             # Font files

/public                # Publicly accessible files
  /js                  # Public JavaScript
    - cacheManager.js
  - service-worker.js

/server                # Server-side code (keep existing structure)
  /routes              # API routes
    - subtasks.js
  /middleware          # Express middleware
  /controllers         # Route controllers
  - dataStorage.js
  - timetableHandler.js

/config                # Configuration files
  - package.json
  - firebase.json
  - .env files

/data                  # Application data (keep existing)
  /static              # Static data files
    - locations.json
    - schedule.json
    - timetable.json
  /energy-logs         # Energy tracking data
  /uploads             # User uploads

/workers               # Web workers
  - imageAnalysis.js
  - priority-calculator-with-worker.js
  - worker.js

/docs                  # Documentation
  - README.md
  - API documentation
  - Architecture guides

MIGRATION PRIORITIES
===================

1. HIGH PRIORITY (Large files that impact performance):
   - grind.html/js/css (201KB HTML + large CSS)
   - ai-researcher.js (101KB)
   - speech-recognition.js (81KB)
   - googleDriveApi.js (72KB)

2. MEDIUM PRIORITY (Functional modules):
   - All manager files (*Manager.js)
   - Service files (*Service.js)
   - Integration files (*Integration.js)

3. LOW PRIORITY (Small utilities and components):
   - UI utilities and helpers
   - Small CSS files
   - Configuration files

BENEFITS OF NEW STRUCTURE
=========================

1. Clear separation of concerns
2. Easier code navigation and maintenance
3. Better build optimization opportunities
4. Improved team collaboration
5. Scalable architecture for future growth
6. Consistent naming conventions
7. Logical grouping by functionality and size
"""
    
    return structure


def main():
    """Main function."""
    views, components, largest_files = analyze_project_structure()
    
    print(f"\n" + "=" * 50)
    print("CATEGORIZATION SUMMARY")
    print("=" * 50)
    print(f"Identified VIEWS (large files): {len(views)}")
    for view in views[:10]:
        print(f"  - {view}")
    
    print(f"\nIdentified COMPONENTS (smaller files): {len(components)}")
    for component in components[:10]:
        print(f"  - {component}")
    
    # Generate and display proposed structure
    proposed_structure = generate_proposed_structure()
    print(proposed_structure)
    
    # Save to file
    with open('proposed_structure_analysis.txt', 'w', encoding='utf-8') as f:
        f.write(proposed_structure)
    
    print("\n" + "=" * 50)
    print("Analysis complete! Check 'proposed_structure_analysis.txt' for full details.")


if __name__ == "__main__":
    main()
