{"summary": {"total_js": 32, "total_css": 36, "js_external": 9, "js_local": 23, "css_external": 6, "css_local": 30}, "javascript": {"all": ["https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "https://cdn.jsdelivr.net/npm/chart.js", "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js", "https://kit.fontawesome.com/51198d7b97.js", "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js", "js/ai-latex-conversion.js", "js/ai-researcher.js", "js/alarm-mini-display.js", "js/alarm-service.js", "js/cacheManager.js", "js/common.js", "js/cross-tab-sync.js", "js/currentTaskManager.js", "js/energyLevels.js", "js/firebase-init.js", "js/grind-speech-synthesis.js", "js/initFirestoreData.js", "js/inject-header.js", "js/pomodoroTimer.js", "js/sideDrawer.js", "js/sleepTimeCalculator.js", "js/storageManager.js", "js/task-notes-injector.js", "js/task-notes.js", "js/taskLinks.js", "js/text-expansion.js", "js/userGuidance.js", "priority-calculator.js"], "external": ["https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "https://cdn.jsdelivr.net/npm/chart.js", "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js", "https://kit.fontawesome.com/51198d7b97.js", "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js"], "local": ["js/ai-latex-conversion.js", "js/ai-researcher.js", "js/alarm-mini-display.js", "js/alarm-service.js", "js/cacheManager.js", "js/common.js", "js/cross-tab-sync.js", "js/currentTaskManager.js", "js/energyLevels.js", "js/firebase-init.js", "js/grind-speech-synthesis.js", "js/initFirestoreData.js", "js/inject-header.js", "js/pomodoroTimer.js", "js/sideDrawer.js", "js/sleepTimeCalculator.js", "js/storageManager.js", "js/task-notes-injector.js", "js/task-notes.js", "js/taskLinks.js", "js/text-expansion.js", "js/userGuidance.js", "priority-calculator.js"]}, "css": {"all": ["css/academic-details.css", "css/ai-search-response.css", "css/alarm-service.css", "css/daily-calendar.css", "css/extracted.css", "css/flashcards.css", "css/notification.css", "css/priority-calculator.css", "css/priority-list.css", "css/search-modal.css", "css/settings.css", "css/sideDrawer.css", "css/simulation-enhancer.css", "css/sleep-saboteurs.css", "css/study-spaces.css", "css/subject-marks.css", "css/task-display.css", "css/task-notes.css", "css/taskLinks.css", "css/test-feedback.css", "css/text-expansion.css", "css/workspace.css", "grind.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.quilljs.com/1.3.6/quill.snow.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap", "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap", "main.css", "relaxed-mode/style.css", "styles/calendar.css", "styles/index.css", "styles/main.css", "styles/study-spaces.css", "styles/tasks.css"], "external": ["https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.quilljs.com/1.3.6/quill.snow.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap", "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap"], "local": ["css/academic-details.css", "css/ai-search-response.css", "css/alarm-service.css", "css/daily-calendar.css", "css/extracted.css", "css/flashcards.css", "css/notification.css", "css/priority-calculator.css", "css/priority-list.css", "css/search-modal.css", "css/settings.css", "css/sideDrawer.css", "css/simulation-enhancer.css", "css/sleep-saboteurs.css", "css/study-spaces.css", "css/subject-marks.css", "css/task-display.css", "css/task-notes.css", "css/taskLinks.css", "css/test-feedback.css", "css/text-expansion.css", "css/workspace.css", "grind.css", "main.css", "relaxed-mode/style.css", "styles/calendar.css", "styles/index.css", "styles/main.css", "styles/study-spaces.css", "styles/tasks.css"]}, "external_domains": {"cdn.jsdelivr.net": 6, "cdnjs.cloudflare.com": 2, "www.gstatic.com": 2, "fonts.googleapis.com": 2, "kit.fontawesome.com": 1, "unpkg.com": 1, "cdn.quilljs.com": 1}, "local_directories": {"js": 22, "css": 22, "styles": 5, ".": 3, "relaxed-mode": 1}}