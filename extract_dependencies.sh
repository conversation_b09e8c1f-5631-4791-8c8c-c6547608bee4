#!/bin/bash
# Dependency Mapper Agent - Bash Implementation
# Task: Extract <script> and <link> tags, map references.

echo "Dependency Mapper Agent - Extracting web dependencies..."
echo "======================================================="

# Extract JavaScript dependencies
echo "Extracting JavaScript dependencies..."
grep -RohE '<script[^>]+src="[^"]+"' . | sed -E 's/.*src="([^"]+)".*/\1/' | sort -u > js_deps.txt

# Also extract script tags with single quotes
grep -RohE "<script[^>]+src='[^']+'" . | sed -E "s/.*src='([^']+)'.*/\1/" | sort -u >> js_deps.txt

# Extract module script dependencies
grep -RohE '<script[^>]+type="module"[^>]+src="[^"]+"' . | sed -E 's/.*src="([^"]+)".*/\1/' | sort -u >> js_deps.txt
grep -RohE '<script[^>]+src="[^"]+"[^>]+type="module"' . | sed -E 's/.*src="([^"]+)".*/\1/' | sort -u >> js_deps.txt

# Remove duplicates from js_deps.txt
sort -u js_deps.txt -o js_deps.txt

# Extract CSS dependencies
echo "Extracting CSS dependencies..."
grep -RohE '<link[^>]+href="[^"]+"\s*rel="stylesheet"' . | sed -E 's/.*href="([^"]+)".*/\1/' | sort -u > css_deps.txt

# Also extract link tags with different attribute orders
grep -RohE '<link[^>]+rel="stylesheet"[^>]+href="[^"]+"' . | sed -E 's/.*href="([^"]+)".*/\1/' | sort -u >> css_deps.txt

# Extract preload CSS links
grep -RohE '<link[^>]+rel="preload"[^>]+href="[^"]+\.css"' . | sed -E 's/.*href="([^"]+)".*/\1/' | sort -u >> css_deps.txt

# Extract links with single quotes
grep -RohE "<link[^>]+href='[^']+'\s*rel='stylesheet'" . | sed -E "s/.*href='([^']+)'.*/\1/" | sort -u >> css_deps.txt
grep -RohE "<link[^>]+rel='stylesheet'[^>]+href='[^']+'" . | sed -E "s/.*href='([^']+)'.*/\1/" | sort -u >> css_deps.txt

# Remove duplicates from css_deps.txt
sort -u css_deps.txt -o css_deps.txt

# Check if files were created successfully
if [ -f "js_deps.txt" ] && [ -f "css_deps.txt" ]; then
    echo "✓ Dependency extraction completed successfully"
    
    js_count=$(wc -l < js_deps.txt)
    css_count=$(wc -l < css_deps.txt)
    
    echo ""
    echo "Summary:"
    echo "========"
    echo "JavaScript dependencies: $js_count"
    echo "CSS dependencies: $css_count"
    echo "Total dependencies: $((js_count + css_count))"
    
    echo ""
    echo "Preview of JavaScript dependencies (first 10):"
    echo "=============================================="
    head -10 js_deps.txt
    
    echo ""
    echo "Preview of CSS dependencies (first 10):"
    echo "======================================="
    head -10 css_deps.txt
    
    echo ""
    echo "External vs Local Dependencies:"
    echo "=============================="
    
    # Count external dependencies (those starting with http/https)
    js_external=$(grep -c '^https\?://' js_deps.txt || echo "0")
    css_external=$(grep -c '^https\?://' css_deps.txt || echo "0")
    
    js_local=$((js_count - js_external))
    css_local=$((css_count - css_external))
    
    echo "JavaScript - External: $js_external, Local: $js_local"
    echo "CSS - External: $css_external, Local: $css_local"
    
    echo ""
    echo "Most Common External Dependencies:"
    echo "================================="
    
    # Combine and count external dependencies
    (grep '^https\?://' js_deps.txt; grep '^https\?://' css_deps.txt) | \
    sort | uniq -c | sort -nr | head -5 | \
    while read count url; do
        echo "  ${count}x: $url"
    done
    
    echo ""
    echo "Files generated:"
    echo "==============="
    echo "- js_deps.txt (JavaScript dependencies)"
    echo "- css_deps.txt (CSS dependencies)"
    
else
    echo "✗ Error: Failed to create dependency files"
    exit 1
fi

echo ""
echo "Dependency extraction complete!"
