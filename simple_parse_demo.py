#!/usr/bin/env python3
"""
Simple dependency parsing demo - implements the exact Python snippet from requirements
"""

import json

# Implement the exact Python snippet from the requirements
try:
    deps = {
        'js': open('js_deps.txt').read().split(), 
        'css': open('css_deps.txt').read().split()
    }
    print(json.dumps(deps, indent=2))
    
    # Additional analysis
    print(f"\nDependency Summary:")
    print(f"JavaScript dependencies: {len(deps['js'])}")
    print(f"CSS dependencies: {len(deps['css'])}")
    print(f"Total dependencies: {len(deps['js']) + len(deps['css'])}")
    
    # Categorize external vs local
    external_js = [dep for dep in deps['js'] if dep.startswith(('http://', 'https://'))]
    local_js = [dep for dep in deps['js'] if not dep.startswith(('http://', 'https://'))]
    external_css = [dep for dep in deps['css'] if dep.startswith(('http://', 'https://'))]
    local_css = [dep for dep in deps['css'] if not dep.startswith(('http://', 'https://'))]
    
    print(f"\nBreakdown:")
    print(f"External JS: {len(external_js)}, Local JS: {len(local_js)}")
    print(f"External CSS: {len(external_css)}, Local CSS: {len(local_css)}")
    
except FileNotFoundError as e:
    print(f"Error: {e}")
    print("Please run the dependency extraction script first.")
