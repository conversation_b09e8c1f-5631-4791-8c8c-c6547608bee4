#!/usr/bin/env python3
"""
Module Planner Agent
Task: Propose target structure based on file analysis and best practices.

This script analyzes the current project structure and proposes an optimized
modular organization based on file sizes, functionality, and modern web development practices.
"""

import json
import csv
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple
import argparse


class Mo<PERSON>lePlanner:
    """Module Planner Agent for proposing optimized project structure."""
    
    def __init__(self, root_dir: str = "."):
        self.root_dir = Path(root_dir).resolve()
        self.current_structure = {}
        self.file_analysis = {}
        self.largest_files = []
        self.proposed_structure = {}
        
    def load_audit_data(self, audit_file: str = "audit_report.csv"):
        """Load file audit data to understand current file sizes and distribution."""
        try:
            with open(audit_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                files = list(reader)
            
            # Sort by size (largest first)
            self.largest_files = sorted(files, key=lambda x: int(x['size']), reverse=True)
            
            # Categorize files by type and size
            for file_info in files:
                path = file_info['path']
                size = int(file_info['size'])
                
                # Determine file type
                if path.endswith('.html'):
                    file_type = 'html'
                elif path.endswith('.css'):
                    file_type = 'css'
                elif path.endswith('.js'):
                    file_type = 'js'
                else:
                    file_type = 'other'
                
                # Determine if it's a view or component based on size and naming
                category = self.categorize_file(path, size, file_type)
                
                self.file_analysis[path] = {
                    'size': size,
                    'type': file_type,
                    'category': category,
                    'current_dir': str(Path(path).parent) if '/' in path else 'root'
                }
            
            print(f"Loaded analysis for {len(files)} files")
            return True
            
        except FileNotFoundError:
            print(f"Error: {audit_file} not found. Please run the file auditor first.")
            return False
    
    def categorize_file(self, path: str, size: int, file_type: str) -> str:
        """Categorize files based on size, naming patterns, and functionality."""
        filename = Path(path).name.lower()
        
        # HTML files - distinguish between views and components
        if file_type == 'html':
            # Large HTML files (>15KB) are likely full views/pages
            if size > 15000:
                return 'view'
            # Smaller HTML files might be components or simple pages
            elif 'modal' in filename or 'component' in filename or 'partial' in filename:
                return 'component'
            else:
                return 'view'
        
        # JavaScript files - categorize by functionality
        elif file_type == 'js':
            # Large JS files (>30KB) are likely major features/views
            if size > 30000:
                return 'view_controller'
            # Manager/Service files
            elif any(keyword in filename for keyword in ['manager', 'service', 'api', 'config', 'auth']):
                return 'service'
            # UI/Component files
            elif any(keyword in filename for keyword in ['ui', 'component', 'widget', 'display']):
                return 'component'
            # Utility files
            elif any(keyword in filename for keyword in ['util', 'helper', 'common', 'tool']):
                return 'utility'
            # Integration files
            elif any(keyword in filename for keyword in ['integration', 'sync', 'connector']):
                return 'integration'
            else:
                return 'module'
        
        # CSS files - categorize by scope
        elif file_type == 'css':
            # Large CSS files (>20KB) are likely main stylesheets or view-specific
            if size > 20000:
                return 'view_style'
            # Component-specific styles
            elif any(keyword in filename for keyword in ['component', 'widget', 'modal']):
                return 'component_style'
            # Layout styles
            elif any(keyword in filename for keyword in ['layout', 'grid', 'nav', 'header', 'footer']):
                return 'layout_style'
            else:
                return 'module_style'
        
        return 'other'
    
    def analyze_current_structure(self):
        """Analyze the current project structure."""
        structure = defaultdict(list)
        
        for file_path, info in self.file_analysis.items():
            current_dir = info['current_dir']
            structure[current_dir].append({
                'path': file_path,
                'size': info['size'],
                'type': info['type'],
                'category': info['category']
            })
        
        self.current_structure = dict(structure)
        return self.current_structure
    
    def propose_target_structure(self):
        """Propose an optimized target structure."""
        
        # Initialize the proposed structure
        proposed = {
            'src': {
                'views': {},
                'components': {},
                'layouts': {},
                'services': {},
                'utils': {},
                'integrations': {},
                'styles': {
                    'views': {},
                    'components': {},
                    'layouts': {},
                    'base': {}
                },
                'scripts': {
                    'views': {},
                    'components': {},
                    'services': {},
                    'utils': {}
                },
                'assets': {
                    'images': {},
                    'icons': {},
                    'sounds': {},
                    'fonts': {}
                }
            },
            'public': {},
            'server': {},
            'config': {},
            'data': {},
            'docs': {}
        }
        
        # Categorize files into the new structure
        for file_path, info in self.file_analysis.items():
            category = info['category']
            file_type = info['type']
            size = info['size']
            
            # Determine target location based on category and type
            target_path = self.determine_target_path(file_path, category, file_type, size)
            
            # Add to proposed structure
            self.add_to_proposed_structure(proposed, target_path, file_path, info)
        
        self.proposed_structure = proposed
        return proposed
    
    def determine_target_path(self, file_path: str, category: str, file_type: str, size: int) -> str:
        """Determine the target path for a file in the new structure."""
        filename = Path(file_path).name
        
        # HTML files
        if file_type == 'html':
            if category == 'view' or size > 15000:
                return f"src/views/{filename}"
            else:
                return f"src/components/{filename}"
        
        # JavaScript files
        elif file_type == 'js':
            if category == 'view_controller':
                return f"src/scripts/views/{filename}"
            elif category == 'service':
                return f"src/services/{filename}"
            elif category == 'component':
                return f"src/scripts/components/{filename}"
            elif category == 'utility':
                return f"src/utils/{filename}"
            elif category == 'integration':
                return f"src/integrations/{filename}"
            else:
                return f"src/scripts/{filename}"
        
        # CSS files
        elif file_type == 'css':
            if category == 'view_style':
                return f"src/styles/views/{filename}"
            elif category == 'component_style':
                return f"src/styles/components/{filename}"
            elif category == 'layout_style':
                return f"src/styles/layouts/{filename}"
            else:
                return f"src/styles/base/{filename}"
        
        # Special handling for specific directories
        if file_path.startswith('server/'):
            return f"server/{filename}"
        elif file_path.startswith('public/'):
            return f"public/{filename}"
        elif file_path.startswith('data/'):
            return f"data/{filename}"
        elif file_path.startswith('assets/'):
            return f"src/assets/{filename}"
        elif file_path.startswith('sounds/'):
            return f"src/assets/sounds/{filename}"
        elif file_path.startswith('icons/'):
            return f"src/assets/icons/{filename}"
        
        return f"src/{filename}"
    
    def add_to_proposed_structure(self, structure: dict, target_path: str, original_path: str, info: dict):
        """Add a file to the proposed structure."""
        # This is a simplified representation - in practice, you'd build a nested dict
        if 'file_mappings' not in structure:
            structure['file_mappings'] = {}
        
        structure['file_mappings'][original_path] = {
            'target_path': target_path,
            'size': info['size'],
            'type': info['type'],
            'category': info['category']
        }
    
    def generate_structure_tree(self) -> str:
        """Generate a visual tree representation of the proposed structure."""
        tree = """
/src
  /components          # Reusable UI components (<15KB HTML, component JS/CSS)
    /ui                # Basic UI components
    /forms             # Form components
    /modals            # Modal dialogs
    /widgets           # Interactive widgets
  
  /views               # Main application views/pages (>15KB HTML files)
    /pages             # Full page views
    /layouts           # Page layout templates
  
  /services            # Business logic and API services
    /api               # API communication
    /auth              # Authentication services
    /data              # Data management
    /external          # Third-party integrations
  
  /utils               # Utility functions and helpers
    /helpers           # General helper functions
    /validators        # Input validation
    /formatters        # Data formatting
  
  /integrations        # External service integrations
    /firebase          # Firebase integration
    /google            # Google services
    /third-party       # Other external APIs
  
  /styles              # Organized stylesheets
    /base              # Base styles, variables, mixins
    /layouts           # Layout-specific styles
    /components        # Component-specific styles
    /views             # View-specific styles
    /themes            # Theme variations
  
  /scripts             # Organized JavaScript
    /views             # View controllers (>30KB JS files)
    /components        # Component scripts
    /services          # Service implementations
    /utils             # Utility scripts
  
  /assets              # Static assets
    /images            # Image files
    /icons             # Icon files
    /sounds            # Audio files
    /fonts             # Font files

/public                # Publicly accessible files
  /cache               # Cache-related files
  /service-worker.js   # Service worker

/server                # Server-side code
  /routes              # API routes
  /middleware          # Express middleware
  /models              # Data models
  /controllers         # Route controllers

/config                # Configuration files
  /env                 # Environment configurations
  /build               # Build configurations

/data                  # Application data
  /static              # Static data files
  /uploads             # User uploads
  /cache               # Data cache

/docs                  # Documentation
  /api                 # API documentation
  /guides              # User guides
  /architecture        # Architecture docs
"""
        return tree
    
    def generate_migration_plan(self) -> List[Dict]:
        """Generate a step-by-step migration plan."""
        if not self.proposed_structure or 'file_mappings' not in self.proposed_structure:
            return []
        
        migration_steps = []
        
        # Group files by target directory for efficient migration
        target_dirs = defaultdict(list)
        for original_path, mapping in self.proposed_structure['file_mappings'].items():
            target_dir = str(Path(mapping['target_path']).parent)
            target_dirs[target_dir].append({
                'original': original_path,
                'target': mapping['target_path'],
                'size': mapping['size'],
                'type': mapping['type']
            })
        
        # Create migration steps
        step_num = 1
        for target_dir, files in sorted(target_dirs.items()):
            migration_steps.append({
                'step': step_num,
                'action': 'create_directory',
                'target': target_dir,
                'description': f"Create directory structure: {target_dir}"
            })
            step_num += 1
            
            for file_info in files:
                migration_steps.append({
                    'step': step_num,
                    'action': 'move_file',
                    'source': file_info['original'],
                    'target': file_info['target'],
                    'size': file_info['size'],
                    'type': file_info['type'],
                    'description': f"Move {file_info['original']} → {file_info['target']}"
                })
                step_num += 1
        
        return migration_steps
    
    def print_analysis_summary(self):
        """Print a summary of the analysis and recommendations."""
        print(f"\n{'='*60}")
        print("MODULE PLANNER ANALYSIS SUMMARY")
        print(f"{'='*60}")
        
        # Current structure analysis
        print(f"Current Structure Analysis:")
        print(f"Total files analyzed: {len(self.file_analysis)}")
        
        # File type distribution
        type_counts = defaultdict(int)
        category_counts = defaultdict(int)
        for info in self.file_analysis.values():
            type_counts[info['type']] += 1
            category_counts[info['category']] += 1
        
        print(f"\nFile Type Distribution:")
        for file_type, count in sorted(type_counts.items()):
            print(f"  {file_type.upper()}: {count} files")
        
        print(f"\nFile Category Distribution:")
        for category, count in sorted(category_counts.items()):
            print(f"  {category}: {count} files")
        
        # Largest files analysis
        print(f"\nLargest Files (driving views vs components split):")
        for i, file_info in enumerate(self.largest_files[:10]):
            path = file_info['path']
            size = int(file_info['size'])
            category = self.file_analysis[path]['category']
            print(f"  {i+1}. {path} ({size:,} bytes) → {category}")
        
        # Current directory distribution
        print(f"\nCurrent Directory Distribution:")
        dir_counts = defaultdict(int)
        for info in self.file_analysis.values():
            dir_counts[info['current_dir']] += 1
        
        for directory, count in sorted(dir_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"  {directory}: {count} files")
        
        print(f"\n{'='*60}")
    
    def save_reports(self):
        """Save analysis and migration reports."""
        # Save detailed analysis
        analysis_report = {
            'summary': {
                'total_files': len(self.file_analysis),
                'largest_files': self.largest_files[:20],
                'current_structure': self.current_structure
            },
            'file_analysis': self.file_analysis,
            'proposed_structure': self.proposed_structure,
            'migration_plan': self.generate_migration_plan()
        }
        
        with open('module_planning_report.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_report, f, indent=2, ensure_ascii=False)
        
        # Save migration plan as CSV
        migration_steps = self.generate_migration_plan()
        with open('migration_plan.csv', 'w', newline='', encoding='utf-8') as f:
            if migration_steps:
                fieldnames = migration_steps[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(migration_steps)
        
        # Save structure tree
        with open('proposed_structure.txt', 'w', encoding='utf-8') as f:
            f.write(self.generate_structure_tree())
        
        print("Reports saved:")
        print("- module_planning_report.json (Detailed analysis)")
        print("- migration_plan.csv (Step-by-step migration)")
        print("- proposed_structure.txt (Target structure tree)")


def main():
    """Main function to run the Module Planner Agent."""
    parser = argparse.ArgumentParser(description="Module Planner Agent - Propose optimized project structure")
    parser.add_argument("--dir", "-d", default=".", help="Project directory (default: current directory)")
    parser.add_argument("--audit-file", default="audit_report.csv", help="File audit CSV file")
    parser.add_argument("--quiet", "-q", action="store_true", help="Suppress detailed output")
    
    args = parser.parse_args()
    
    # Create planner instance
    planner = ModulePlanner(args.dir)
    
    # Load audit data
    if not planner.load_audit_data(args.audit_file):
        return
    
    # Analyze current structure
    planner.analyze_current_structure()
    
    # Propose target structure
    planner.propose_target_structure()
    
    # Print analysis unless quiet mode
    if not args.quiet:
        planner.print_analysis_summary()
        print("\nProposed Target Structure:")
        print(planner.generate_structure_tree())
    
    # Save reports
    planner.save_reports()


if __name__ == "__main__":
    main()
