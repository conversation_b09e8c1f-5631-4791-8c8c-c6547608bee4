{"summary": {"total_js": 32, "total_css": 36, "external_js": 9, "local_js": 23, "external_css": 6, "local_css": 30}, "dependencies": {"js": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js", "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js", "https://cdn.jsdelivr.net/npm/chart.js", "https://kit.fontawesome.com/51198d7b97.js", "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "js/cross-tab-sync.js", "js/initFirestoreData.js", "js/common.js", "js/userGuidance.js", "js/ai-researcher.js", "js/firebase-init.js", "js/inject-header.js", "js/storageManager.js", "js/grind-speech-synthesis.js", "js/taskLinks.js", "js/currentTaskManager.js", "js/sleepTimeCalculator.js", "js/energyLevels.js", "js/sideDrawer.js", "js/pomodoroTimer.js", "js/task-notes-injector.js", "js/task-notes.js", "js/text-expansion.js", "js/ai-latex-conversion.js", "js/cacheManager.js", "js/alarm-service.js", "js/alarm-mini-display.js", "priority-calculator.js"], "css": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap", "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap", "https://cdn.quilljs.com/1.3.6/quill.snow.css", "css/taskLinks.css", "css/search-modal.css", "css/sideDrawer.css", "css/task-display.css", "css/text-expansion.css", "css/simulation-enhancer.css", "css/ai-search-response.css", "css/task-notes.css", "css/alarm-service.css", "css/workspace.css", "css/extracted.css", "css/academic-details.css", "css/daily-calendar.css", "css/flashcards.css", "css/notification.css", "css/priority-calculator.css", "css/priority-list.css", "css/settings.css", "css/sleep-saboteurs.css", "css/study-spaces.css", "css/subject-marks.css", "css/test-feedback.css", "grind.css", "main.css", "styles/calendar.css", "styles/index.css", "styles/main.css", "styles/study-spaces.css", "styles/tasks.css", "relaxed-mode/style.css"]}, "analysis": {"external_js": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js", "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js", "https://cdn.jsdelivr.net/npm/chart.js", "https://kit.fontawesome.com/51198d7b97.js", "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"], "local_js": ["js/cross-tab-sync.js", "js/initFirestoreData.js", "js/common.js", "js/userGuidance.js", "js/ai-researcher.js", "js/firebase-init.js", "js/inject-header.js", "js/storageManager.js", "js/grind-speech-synthesis.js", "js/taskLinks.js", "js/currentTaskManager.js", "js/sleepTimeCalculator.js", "js/energyLevels.js", "js/sideDrawer.js", "js/pomodoroTimer.js", "js/task-notes-injector.js", "js/task-notes.js", "js/text-expansion.js", "js/ai-latex-conversion.js", "js/cacheManager.js", "js/alarm-service.js", "js/alarm-mini-display.js", "priority-calculator.js"], "external_css": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap", "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap", "https://cdn.quilljs.com/1.3.6/quill.snow.css"], "local_css": ["css/taskLinks.css", "css/search-modal.css", "css/sideDrawer.css", "css/task-display.css", "css/text-expansion.css", "css/simulation-enhancer.css", "css/ai-search-response.css", "css/task-notes.css", "css/alarm-service.css", "css/workspace.css", "css/extracted.css", "css/academic-details.css", "css/daily-calendar.css", "css/flashcards.css", "css/notification.css", "css/priority-calculator.css", "css/priority-list.css", "css/settings.css", "css/sleep-saboteurs.css", "css/study-spaces.css", "css/subject-marks.css", "css/test-feedback.css", "grind.css", "main.css", "styles/calendar.css", "styles/index.css", "styles/main.css", "styles/study-spaces.css", "styles/tasks.css", "relaxed-mode/style.css"], "external_domains": {"cdn.jsdelivr.net": 6, "cdnjs.cloudflare.com": 2, "unpkg.com": 1, "www.gstatic.com": 2, "kit.fontawesome.com": 1, "fonts.googleapis.com": 2, "cdn.quilljs.com": 1}, "local_directories": {"js": 22, ".": 3, "css": 22, "styles": 5, "relaxed-mode": 1}}, "external_domains": {"cdn.jsdelivr.net": 6, "cdnjs.cloudflare.com": 2, "unpkg.com": 1, "www.gstatic.com": 2, "kit.fontawesome.com": 1, "fonts.googleapis.com": 2, "cdn.quilljs.com": 1}, "local_directories": {"js": 22, ".": 3, "css": 22, "styles": 5, "relaxed-mode": 1}}