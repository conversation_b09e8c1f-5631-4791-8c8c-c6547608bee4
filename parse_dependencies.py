#!/usr/bin/env python3
"""
Parse dependencies from js_deps.txt and css_deps.txt
This implements the Python snippet mentioned in the requirements.
"""

import json
import os
from urllib.parse import urlparse
from collections import Counter


def parse_dependencies():
    """Parse the dependency files and create a structured JSON output."""
    
    if not os.path.exists('js_deps.txt') or not os.path.exists('css_deps.txt'):
        print("Error: js_deps.txt or css_deps.txt not found. Please run the extraction script first.")
        return
    
    try:
        # Read JavaScript dependencies
        with open('js_deps.txt', 'r', encoding='utf-8') as f:
            js_deps = [line.strip() for line in f if line.strip()]
        
        # Read CSS dependencies
        with open('css_deps.txt', 'r', encoding='utf-8') as f:
            css_deps = [line.strip() for line in f if line.strip()]
        
        # Basic structure as specified in requirements
        deps = {
            'js': js_deps,
            'css': css_deps
        }
        
        # Print the basic JSON structure
        print("Basic dependency structure:")
        print(json.dumps(deps, indent=2))
        
        # Enhanced analysis
        print("\n" + "="*50)
        print("ENHANCED DEPENDENCY ANALYSIS")
        print("="*50)
        
        # Categorize dependencies
        js_external = [dep for dep in js_deps if is_external_url(dep)]
        js_local = [dep for dep in js_deps if not is_external_url(dep)]
        css_external = [dep for dep in css_deps if is_external_url(dep)]
        css_local = [dep for dep in css_deps if not is_external_url(dep)]
        
        enhanced_deps = {
            'summary': {
                'total_js': len(js_deps),
                'total_css': len(css_deps),
                'js_external': len(js_external),
                'js_local': len(js_local),
                'css_external': len(css_external),
                'css_local': len(css_local)
            },
            'javascript': {
                'all': js_deps,
                'external': js_external,
                'local': js_local
            },
            'css': {
                'all': css_deps,
                'external': css_external,
                'local': css_local
            },
            'external_domains': analyze_external_domains(js_external + css_external),
            'local_directories': analyze_local_paths(js_local + css_local)
        }
        
        # Save enhanced analysis
        with open('dependency_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(enhanced_deps, f, indent=2, ensure_ascii=False)
        
        print(f"Total dependencies found: {len(js_deps) + len(css_deps)}")
        print(f"JavaScript: {len(js_deps)} ({len(js_external)} external, {len(js_local)} local)")
        print(f"CSS: {len(css_deps)} ({len(css_external)} external, {len(css_local)} local)")
        
        print("\nTop external domains:")
        for domain, count in enhanced_deps['external_domains'].items():
            print(f"  {domain}: {count} dependencies")
        
        print("\nLocal directory distribution:")
        for directory, count in enhanced_deps['local_directories'].items():
            print(f"  {directory}: {count} files")
        
        print("\n✓ dependency_analysis.json created with detailed analysis")
        
    except Exception as e:
        print(f"Error processing dependency files: {e}")


def is_external_url(url: str) -> bool:
    """Check if URL is external (has protocol and domain)."""
    parsed = urlparse(url)
    return bool(parsed.scheme and parsed.netloc)


def analyze_external_domains(external_urls: list) -> dict:
    """Analyze external domains and their usage frequency."""
    domains = []
    for url in external_urls:
        parsed = urlparse(url)
        if parsed.netloc:
            domains.append(parsed.netloc)
    
    domain_counts = Counter(domains)
    return dict(domain_counts.most_common())


def analyze_local_paths(local_paths: list) -> dict:
    """Analyze local file paths and directory distribution."""
    directories = []
    for path in local_paths:
        if '/' in path:
            directory = path.split('/')[0]
        else:
            directory = '.' # Root directory
        directories.append(directory)
    
    dir_counts = Counter(directories)
    return dict(dir_counts.most_common())


def generate_dependency_graph():
    """Generate a simple dependency graph visualization data."""
    try:
        with open('dependency_analysis.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Create graph data for visualization
        graph_data = {
            'nodes': [],
            'edges': [],
            'stats': data['summary']
        }
        
        # Add domain nodes
        for domain, count in data['external_domains'].items():
            graph_data['nodes'].append({
                'id': domain,
                'type': 'external_domain',
                'count': count,
                'size': min(count * 10, 100)  # Scale for visualization
            })
        
        # Add local directory nodes
        for directory, count in data['local_directories'].items():
            graph_data['nodes'].append({
                'id': directory,
                'type': 'local_directory', 
                'count': count,
                'size': min(count * 5, 50)
            })
        
        with open('dependency_graph.json', 'w', encoding='utf-8') as f:
            json.dump(graph_data, f, indent=2)
        
        print("✓ dependency_graph.json created for visualization")
        
    except Exception as e:
        print(f"Error generating dependency graph: {e}")


if __name__ == "__main__":
    parse_dependencies()
    generate_dependency_graph()
