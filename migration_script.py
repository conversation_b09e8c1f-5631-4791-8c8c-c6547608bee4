#!/usr/bin/env python3
"""
Migration Script for Module Planner
Generates commands to restructure the project according to the proposed structure.
"""

import csv
from pathlib import Path


def generate_migration_commands():
    """Generate shell commands to migrate to the new structure."""
    
    print("Module Planner - Migration Script Generator")
    print("=" * 50)
    
    # Load audit data to get file sizes
    try:
        with open('audit_report.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            files = list(reader)
    except FileNotFoundError:
        print("Error: audit_report.csv not found.")
        return
    
    # Create file size lookup
    file_sizes = {file_info['path']: int(file_info['size']) for file_info in files}
    
    # Migration mappings based on analysis
    migrations = []
    
    # Create directory structure first
    directories = [
        "src/components/ui",
        "src/components/forms", 
        "src/components/widgets",
        "src/components/modals",
        "src/views/pages",
        "src/views/controllers",
        "src/services/api",
        "src/services/auth",
        "src/services/data",
        "src/services/integrations",
        "src/utils/helpers",
        "src/utils/managers",
        "src/styles/base",
        "src/styles/views",
        "src/styles/components",
        "src/styles/layouts",
        "src/assets/images",
        "src/assets/sounds",
        "src/assets/icons",
        "workers",
        "config"
    ]
    
    print("# Step 1: Create directory structure")
    for directory in directories:
        print(f"mkdir -p {directory}")
    
    print("\n# Step 2: Migrate large view files (>15KB HTML)")
    view_files = [
        ("grind.html", "src/views/pages/grind.html"),
        ("extracted.html", "src/views/pages/extracted.html"),
        ("landing.html", "src/views/pages/landing.html"),
        ("academic-details.html", "src/views/pages/academic-details.html"),
        ("workspace.html", "src/views/pages/workspace.html"),
        ("tasks.html", "src/views/pages/tasks.html"),
        ("instant-test-feedback.html", "src/views/pages/instant-test-feedback.html"),
        ("study-spaces.html", "src/views/pages/study-spaces.html"),
        ("flashcards.html", "src/views/pages/flashcards.html")
    ]
    
    for source, target in view_files:
        size = file_sizes.get(source, 0)
        print(f"mv {source} {target}  # {size:,} bytes")
    
    print("\n# Step 3: Migrate large view controllers (>30KB JS)")
    controller_files = [
        ("js/ai-researcher.js", "src/views/controllers/ai-researcher.js"),
        ("js/speech-recognition.js", "src/views/controllers/speech-recognition.js"),
        ("js/googleDriveApi.js", "src/views/controllers/googleDriveApi.js"),
        ("js/semester-management.js", "src/views/controllers/semester-management.js"),
        ("js/studySpacesManager.js", "src/views/controllers/studySpacesManager.js"),
        ("js/test-feedback.js", "src/views/controllers/test-feedback.js"),
        ("js/flashcards.js", "src/views/controllers/flashcards.js"),
        ("js/flashcardManager.js", "src/views/controllers/flashcardManager.js"),
        ("js/calendarManager.js", "src/views/controllers/calendarManager.js"),
        ("js/pomodoroTimer.js", "src/views/controllers/pomodoroTimer.js"),
        ("js/grind-speech-synthesis.js", "src/views/controllers/grind-speech-synthesis.js"),
        ("js/calendar-views.js", "src/views/controllers/calendar-views.js")
    ]
    
    for source, target in controller_files:
        size = file_sizes.get(source, 0)
        if size > 30000:
            print(f"mv {source} {target}  # {size:,} bytes")
    
    print("\n# Step 4: Migrate large view styles (>20KB CSS)")
    view_styles = [
        ("grind.css", "src/styles/views/grind.css"),
        ("css/daily-calendar.css", "src/styles/views/daily-calendar.css"),
        ("css/extracted.css", "src/styles/views/extracted.css"),
        ("css/workspace.css", "src/styles/views/workspace.css"),
        ("css/academic-details.css", "src/styles/views/academic-details.css")
    ]
    
    for source, target in view_styles:
        size = file_sizes.get(source, 0)
        if size > 20000:
            print(f"mv {source} {target}  # {size:,} bytes")
    
    print("\n# Step 5: Migrate services")
    service_files = [
        # API services
        ("js/gemini-api.js", "src/services/api/gemini-api.js"),
        ("js/googleGenerativeAI.js", "src/services/api/googleGenerativeAI.js"),
        ("js/api-optimization.js", "src/services/api/api-optimization.js"),
        ("js/api-settings.js", "src/services/api/api-settings.js"),
        
        # Auth services
        ("js/auth.js", "src/services/auth/auth.js"),
        ("js/firebaseAuth.js", "src/services/auth/firebaseAuth.js"),
        ("js/firebase-config.js", "src/services/auth/firebase-config.js"),
        ("js/firebase-init.js", "src/services/auth/firebase-init.js"),
        
        # Data services
        ("js/firestore.js", "src/services/data/firestore.js"),
        ("js/storageManager.js", "src/services/data/storageManager.js"),
        ("js/data-sync-manager.js", "src/services/data/data-sync-manager.js"),
        
        # Integrations
        ("js/todoistIntegration.js", "src/services/integrations/todoistIntegration.js"),
        ("js/cross-tab-sync.js", "src/services/integrations/cross-tab-sync.js"),
        ("js/flashcardTaskIntegration.js", "src/services/integrations/flashcardTaskIntegration.js")
    ]
    
    for source, target in service_files:
        size = file_sizes.get(source, 0)
        print(f"mv {source} {target}  # {size:,} bytes")
    
    print("\n# Step 6: Migrate utilities")
    utility_files = [
        # Helpers
        ("js/common.js", "src/utils/helpers/common.js"),
        ("js/ui-utilities.js", "src/utils/helpers/ui-utilities.js"),
        ("js/transitionManager.js", "src/utils/helpers/transitionManager.js"),
        ("js/inject-header.js", "src/utils/helpers/inject-header.js"),
        
        # Managers
        ("js/tasksManager.js", "src/utils/managers/tasksManager.js"),
        ("js/scheduleManager.js", "src/utils/managers/scheduleManager.js"),
        ("js/currentTaskManager.js", "src/utils/managers/currentTaskManager.js"),
        ("js/roleModelManager.js", "src/utils/managers/roleModelManager.js"),
        ("js/recipeManager.js", "src/utils/managers/recipeManager.js")
    ]
    
    for source, target in utility_files:
        size = file_sizes.get(source, 0)
        print(f"mv {source} {target}  # {size:,} bytes")
    
    print("\n# Step 7: Migrate UI components")
    component_files = [
        # UI components
        ("js/sideDrawer.js", "src/components/ui/sideDrawer.js"),
        ("css/sideDrawer.css", "src/components/ui/sideDrawer.css"),
        ("js/clock-display.js", "src/components/ui/clock-display.js"),
        ("js/theme-manager.js", "src/components/ui/theme-manager.js"),
        ("css/notification.css", "src/components/ui/notification.css"),
        
        # Form components
        ("js/task-notes.js", "src/components/forms/task-notes.js"),
        ("css/task-notes.css", "src/components/forms/task-notes.css"),
        ("js/text-expansion.js", "src/components/forms/text-expansion.js"),
        ("css/text-expansion.css", "src/components/forms/text-expansion.css"),
        
        # Widgets
        ("js/energyLevels.js", "src/components/widgets/energyLevels.js"),
        ("js/quoteManager.js", "src/components/widgets/quoteManager.js"),
        ("js/soundManager.js", "src/components/widgets/soundManager.js"),
        ("js/energyHologram.js", "src/components/widgets/energyHologram.js")
    ]
    
    for source, target in component_files:
        size = file_sizes.get(source, 0)
        print(f"mv {source} {target}  # {size:,} bytes")
    
    print("\n# Step 8: Migrate remaining CSS")
    remaining_css = [
        ("main.css", "src/styles/base/main.css"),
        ("styles/main.css", "src/styles/base/main.css"),
        ("css/taskLinks.css", "src/styles/components/taskLinks.css"),
        ("css/priority-list.css", "src/styles/components/priority-list.css"),
        ("css/alarm-service.css", "src/styles/components/alarm-service.css"),
        ("styles/calendar.css", "src/styles/layouts/calendar.css"),
        ("styles/tasks.css", "src/styles/layouts/tasks.css")
    ]
    
    for source, target in remaining_css:
        size = file_sizes.get(source, 0)
        print(f"mv {source} {target}  # {size:,} bytes")
    
    print("\n# Step 9: Migrate assets")
    asset_files = [
        ("sounds/notification.mp3", "src/assets/sounds/notification.mp3"),
        ("sounds/pop.mp3", "src/assets/sounds/pop.mp3"),
        ("alarm-sounds/alarm1.mp3", "src/assets/sounds/alarm1.mp3"),
        ("alarm-sounds/alarm2.mp3", "src/assets/sounds/alarm2.mp3"),
        ("alarm-sounds/alarm3.mp3", "src/assets/sounds/alarm3.mp3")
    ]
    
    for source, target in asset_files:
        print(f"mv {source} {target}")
    
    print("\n# Step 10: Migrate workers")
    worker_files = [
        ("workers/imageAnalysis.js", "workers/imageAnalysis.js"),
        ("priority-calculator-with-worker.js", "workers/priority-calculator-with-worker.js"),
        ("worker.js", "workers/worker.js"),
        ("test-worker.js", "workers/test-worker.js")
    ]
    
    for source, target in worker_files:
        size = file_sizes.get(source, 0)
        print(f"mv {source} {target}  # {size:,} bytes")
    
    print("\n# Step 11: Update import paths in HTML files")
    print("# Note: You'll need to update all import paths in HTML files to reflect new structure")
    print("# Example updates needed:")
    print("# - js/ai-researcher.js → src/views/controllers/ai-researcher.js")
    print("# - css/sideDrawer.css → src/components/ui/sideDrawer.css")
    print("# - grind.css → src/styles/views/grind.css")
    
    print(f"\n# Migration complete!")
    print(f"# Total files to migrate: {len(files)}")
    print(f"# New structure provides clear separation of views, components, and services")


if __name__ == "__main__":
    generate_migration_commands()
