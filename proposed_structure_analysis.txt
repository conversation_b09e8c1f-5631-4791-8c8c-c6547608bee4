
PROPOSED TARGET STRUCTURE
=========================

/src
  /components          # Reusable UI components (<15KB HTML, <30KB JS)
    /ui                # Basic UI elements (buttons, inputs, etc.)
      - sideDrawer.js/css
      - clock-display.js
      - ui-utilities.js
    /forms             # Form components
      - task-notes.js/css
      - text-expansion.js/css
    /modals            # Modal dialogs
      - notification.css
      - compact-style.css
    /widgets           # Interactive widgets
      - energyLevels.js
      - quoteManager.js
      - soundManager.js

  /views               # Main application views/pages (>15KB HTML, >30KB JS)
    /pages             # Full page views
      - grind.html/js/css (201KB HTML, large JS)
      - extracted.html (92KB)
      - landing.html (55KB)
      - workspace.html (21KB)
      - academic-details.html (26KB)
    /controllers       # View controllers (>30KB JS files)
      - ai-researcher.js (101KB)
      - googleDriveApi.js (72KB)
      - speech-recognition.js (81KB)
      - semester-management.js (62KB)
      - studySpacesManager.js (57KB)
      - test-feedback.js (53KB)
      - flashcards.js (52KB)
      - flashcardManager.js (50KB)

  /services            # Business logic and API services
    /api               # API communication
      - gemini-api.js
      - googleGenerativeAI.js
      - api-optimization.js
      - api-settings.js
    /auth              # Authentication
      - auth.js
      - firebaseAuth.js
      - firebase-config.js
    /data              # Data management
      - firestore.js
      - storageManager.js
      - data-sync-manager.js
      - indexedDB.js
    /integrations      # External services
      - todoistIntegration.js
      - cross-tab-sync.js
      - data-sync-integration.js

  /utils               # Utility functions and helpers
    /helpers           # General helpers
      - common.js
      - ui-utilities.js
      - transitionManager.js
    /managers          # Specialized managers
      - calendarManager.js
      - tasksManager.js
      - scheduleManager.js
      - themeManager.js

  /styles              # Organized stylesheets
    /base              # Base styles and variables
      - main.css
      - styles/main.css
    /views             # View-specific styles (>20KB CSS)
      - grind.css (126KB)
      - daily-calendar.css (50KB)
      - extracted.css (40KB)
      - workspace.css (40KB)
    /components        # Component styles
      - sideDrawer.css
      - task-display.css
      - taskLinks.css
      - notification.css
    /layouts           # Layout styles
      - academic-details.css
      - study-spaces.css
      - sleep-saboteurs.css

  /assets              # Static assets
    /images            # Image files
    /icons             # Icon files  
    /sounds            # Audio files
      - notification.mp3
      - pop.mp3
    /fonts             # Font files

/public                # Publicly accessible files
  /js                  # Public JavaScript
    - cacheManager.js
  - service-worker.js

/server                # Server-side code (keep existing structure)
  /routes              # API routes
    - subtasks.js
  /middleware          # Express middleware
  /controllers         # Route controllers
  - dataStorage.js
  - timetableHandler.js

/config                # Configuration files
  - package.json
  - firebase.json
  - .env files

/data                  # Application data (keep existing)
  /static              # Static data files
    - locations.json
    - schedule.json
    - timetable.json
  /energy-logs         # Energy tracking data
  /uploads             # User uploads

/workers               # Web workers
  - imageAnalysis.js
  - priority-calculator-with-worker.js
  - worker.js

/docs                  # Documentation
  - README.md
  - API documentation
  - Architecture guides

MIGRATION PRIORITIES
===================

1. HIGH PRIORITY (Large files that impact performance):
   - grind.html/js/css (201KB HTML + large CSS)
   - ai-researcher.js (101KB)
   - speech-recognition.js (81KB)
   - googleDriveApi.js (72KB)

2. MEDIUM PRIORITY (Functional modules):
   - All manager files (*Manager.js)
   - Service files (*Service.js)
   - Integration files (*Integration.js)

3. LOW PRIORITY (Small utilities and components):
   - UI utilities and helpers
   - Small CSS files
   - Configuration files

BENEFITS OF NEW STRUCTURE
=========================

1. Clear separation of concerns
2. Easier code navigation and maintenance
3. Better build optimization opportunities
4. Improved team collaboration
5. Scalable architecture for future growth
6. Consistent naming conventions
7. Logical grouping by functionality and size
