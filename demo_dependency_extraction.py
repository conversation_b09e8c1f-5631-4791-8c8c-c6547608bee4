#!/usr/bin/env python3
"""
Demo Dependency Extraction
A simplified version to demonstrate the dependency mapping concept.
"""

import re
import json
from pathlib import Path


def extract_dependencies_from_file(file_path):
    """Extract dependencies from a single HTML file."""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Extract script src attributes
        script_pattern = r'<script[^>]+src=["\']([^"\']+)["\'][^>]*>'
        scripts = re.findall(script_pattern, content, re.IGNORECASE)
        
        # Extract link href attributes for stylesheets
        css_pattern = r'<link[^>]+(?:rel=["\']stylesheet["\'][^>]+href=["\']([^"\']+)["\']|href=["\']([^"\']+)["\'][^>]+rel=["\']stylesheet["\'])[^>]*>'
        css_matches = re.findall(css_pattern, content, re.IGNORECASE)
        css_links = [match[0] or match[1] for match in css_matches if match[0] or match[1]]
        
        # Also extract preload CSS
        preload_pattern = r'<link[^>]+rel=["\']preload["\'][^>]+href=["\']([^"\']+\.css)["\'][^>]*>'
        preload_css = re.findall(preload_pattern, content, re.IGNORECASE)
        
        css_links.extend(preload_css)
        
        return {
            'file': str(file_path),
            'scripts': scripts,
            'stylesheets': list(set(css_links))  # Remove duplicates
        }
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return {'file': str(file_path), 'scripts': [], 'stylesheets': []}


def main():
    """Demo the dependency extraction."""
    print("Dependency Mapper Agent - Demo")
    print("=" * 40)
    
    # Sample HTML files to analyze
    html_files = [
        'index.html',
        'grind.html', 
        'workspace.html',
        'extracted.html'
    ]
    
    all_js_deps = set()
    all_css_deps = set()
    file_dependencies = {}
    
    for html_file in html_files:
        file_path = Path(html_file)
        if file_path.exists():
            print(f"\nAnalyzing {html_file}...")
            deps = extract_dependencies_from_file(file_path)
            file_dependencies[html_file] = deps
            
            print(f"  Scripts found: {len(deps['scripts'])}")
            print(f"  Stylesheets found: {len(deps['stylesheets'])}")
            
            # Add to global sets
            all_js_deps.update(deps['scripts'])
            all_css_deps.update(deps['stylesheets'])
            
            # Show first few dependencies
            if deps['scripts']:
                print("  Sample scripts:")
                for script in deps['scripts'][:3]:
                    print(f"    - {script}")
            
            if deps['stylesheets']:
                print("  Sample stylesheets:")
                for css in deps['stylesheets'][:3]:
                    print(f"    - {css}")
        else:
            print(f"File not found: {html_file}")
    
    # Generate simple text files (as per original requirement)
    with open('js_deps.txt', 'w') as f:
        for dep in sorted(all_js_deps):
            f.write(f"{dep}\n")
    
    with open('css_deps.txt', 'w') as f:
        for dep in sorted(all_css_deps):
            f.write(f"{dep}\n")
    
    # Create the JSON structure as specified in requirements
    deps_json = {
        'js': sorted(list(all_js_deps)),
        'css': sorted(list(all_css_deps))
    }
    
    print(f"\n" + "=" * 40)
    print("SUMMARY")
    print("=" * 40)
    print(f"Total JavaScript dependencies: {len(all_js_deps)}")
    print(f"Total CSS dependencies: {len(all_css_deps)}")
    
    # Categorize external vs local
    external_js = [dep for dep in all_js_deps if dep.startswith(('http://', 'https://'))]
    local_js = [dep for dep in all_js_deps if not dep.startswith(('http://', 'https://'))]
    external_css = [dep for dep in all_css_deps if dep.startswith(('http://', 'https://'))]
    local_css = [dep for dep in all_css_deps if not dep.startswith(('http://', 'https://'))]
    
    print(f"External JS: {len(external_js)}, Local JS: {len(local_js)}")
    print(f"External CSS: {len(external_css)}, Local CSS: {len(local_css)}")
    
    print("\nTop External Dependencies:")
    external_deps = external_js + external_css
    for dep in sorted(set(external_deps))[:5]:
        print(f"  - {dep}")
    
    print("\nJSON Structure (as per requirements):")
    print(json.dumps(deps_json, indent=2))
    
    # Save detailed report
    detailed_report = {
        'summary': {
            'total_js': len(all_js_deps),
            'total_css': len(all_css_deps),
            'external_js': len(external_js),
            'local_js': len(local_js),
            'external_css': len(external_css),
            'local_css': len(local_css)
        },
        'dependencies': deps_json,
        'file_mapping': file_dependencies
    }
    
    with open('dependency_demo_report.json', 'w') as f:
        json.dump(detailed_report, f, indent=2)
    
    print(f"\nFiles generated:")
    print("- js_deps.txt")
    print("- css_deps.txt") 
    print("- dependency_demo_report.json")


if __name__ == "__main__":
    main()
