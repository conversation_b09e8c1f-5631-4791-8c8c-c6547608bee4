# Module Planner Agent

A comprehensive tool for analyzing current project structure and proposing optimized modular organization based on file sizes, functionality, and modern web development best practices.

## Overview

The Module Planner Agent analyzes your project's file audit results and proposes a target structure that:

1. **Uses file sizes to drive architectural decisions** (views vs components split)
2. **Follows modern web development patterns** (separation of concerns)
3. **Optimizes for performance** (large files are clearly identified)
4. **Improves maintainability** (logical grouping and hierarchy)
5. **Supports scalability** (room for future growth)

## Key Features

### Size-Based Categorization
- **HTML files >15KB** → **VIEWS** (full pages)
- **HTML files <15KB** → **COMPONENTS** (reusable parts)
- **JS files >30KB** → **VIEW CONTROLLERS** (major features)
- **JS files <30KB** → **MODULES/COMPONENTS** (utilities/services)
- **CSS files >20KB** → **VIEW STYLES** (page-specific)
- **CSS files <20KB** → **COMPONENT STYLES** (reusable)

### Intelligent File Analysis
- Analyzes naming patterns and functionality
- Identifies services, managers, integrations, and utilities
- Groups related files together
- Considers current directory structure

### Migration Planning
- Generates step-by-step migration commands
- Prioritizes large files for performance impact
- Provides shell scripts for easy execution
- Identifies import path updates needed

## Usage

### Basic Analysis

```bash
# Run the main module planner
python module_planner.py

# Run simplified structure analysis
python structure_analysis.py

# Generate migration commands
python migration_script.py
```

### Command Line Options

```bash
# Analyze specific directory
python module_planner.py --dir /path/to/project

# Use custom audit file
python module_planner.py --audit-file custom_audit.csv

# Quiet mode (minimal output)
python module_planner.py --quiet
```

## Analysis Results

### Current Project Analysis

Based on the GPAce project audit:

**File Distribution:**
- **Total files**: 161 web files
- **HTML files**: 18 files (524KB total)
- **CSS files**: 29 files (480KB total)
- **JS files**: 114 files (1576KB total)

**Largest Files (driving structure decisions):**
1. **grind.html** (201,061 bytes) → **VIEW**
2. **grind.css** (125,643 bytes) → **VIEW STYLE**
3. **ai-researcher.js** (101,375 bytes) → **VIEW CONTROLLER**
4. **extracted.html** (92,348 bytes) → **VIEW**
5. **speech-recognition.js** (81,174 bytes) → **VIEW CONTROLLER**

### Proposed Target Structure

```
/src
  /components          # Reusable UI components
    /ui                # Basic UI elements
    /forms             # Form components  
    /widgets           # Interactive widgets
    /modals            # Modal dialogs
  
  /views               # Main application views/pages
    /pages             # Full page views (>15KB HTML)
    /controllers       # View controllers (>30KB JS)
  
  /services            # Business logic and services
    /api               # API communication
    /auth              # Authentication
    /data              # Data management
    /integrations      # External services
  
  /utils               # Utilities and helpers
    /helpers           # General helper functions
    /managers          # Specialized managers
  
  /styles              # Organized stylesheets
    /base              # Base styles and variables
    /views             # View-specific styles (>20KB)
    /components        # Component styles
    /layouts           # Layout styles
  
  /assets              # Static assets
    /images            # Image files
    /sounds            # Audio files
    /icons             # Icon files

/public                # Publicly accessible files
/server                # Server-side code
/workers               # Web workers
/config                # Configuration files
/data                  # Application data
```

## Migration Strategy

### Phase 1: High Priority (Performance Impact)
Focus on largest files that impact loading performance:

1. **grind.html** (201KB) → `src/views/pages/`
2. **grind.css** (126KB) → `src/styles/views/`
3. **ai-researcher.js** (101KB) → `src/views/controllers/`
4. **speech-recognition.js** (81KB) → `src/views/controllers/`

### Phase 2: Medium Priority (Functional Modules)
Organize by functionality:

1. All *Manager.js files → `src/utils/managers/`
2. All *Service.js files → `src/services/`
3. Integration files → `src/services/integrations/`
4. Large view files → `src/views/pages/`

### Phase 3: Low Priority (Components and Utilities)
Complete the reorganization:

1. Small utilities → `src/utils/helpers/`
2. Component CSS → `src/styles/components/`
3. UI components → `src/components/ui/`

## Generated Files

### module_planning_report.json
Comprehensive analysis with:
- File categorization details
- Size-based recommendations
- Current vs proposed structure comparison
- Migration mapping

### migration_plan.csv
Step-by-step migration plan with:
- Source and target paths
- File sizes and types
- Migration priorities
- Shell commands

### proposed_structure.txt
Visual tree representation of:
- Target directory structure
- File organization principles
- Benefits and rationale

## Benefits of New Structure

### 1. Performance Optimization
- **Large files clearly identified**: Easy to implement code splitting
- **Lazy loading opportunities**: Views can be loaded on demand
- **Bundle optimization**: Related files grouped for efficient bundling

### 2. Developer Experience
- **Predictable file locations**: Consistent naming and organization
- **Clear separation of concerns**: Views, components, services, utilities
- **Easier navigation**: Logical hierarchy reduces search time

### 3. Maintainability
- **Related files grouped**: Easier to find and modify related code
- **Consistent patterns**: Reduces cognitive load for developers
- **Scalable architecture**: Structure supports project growth

### 4. Team Collaboration
- **Clear ownership boundaries**: Teams can own specific directories
- **Reduced conflicts**: Better file organization reduces merge conflicts
- **Onboarding efficiency**: New developers can quickly understand structure

## Implementation Notes

### Import Path Updates
After migration, update import paths in HTML files:

```html
<!-- Before -->
<script src="js/ai-researcher.js"></script>
<link href="css/sideDrawer.css" rel="stylesheet">

<!-- After -->
<script src="src/views/controllers/ai-researcher.js"></script>
<link href="src/components/ui/sideDrawer.css" rel="stylesheet">
```

### Build Tool Configuration
Update build tools (Webpack, Vite, etc.) to:
- Use new source directories
- Implement code splitting by views
- Optimize bundle sizes based on file categories

### Server Configuration
Update server routes to serve files from new locations:
- Static file serving paths
- Asset routing
- Cache headers based on file types

## Requirements

- Python 3.6+
- File audit results (audit_report.csv)
- Standard library modules: `csv`, `json`, `pathlib`, `collections`

## Integration

The Module Planner Agent integrates with:
- **File Auditor Agent**: Uses audit results for size-based decisions
- **Dependency Mapper Agent**: Considers dependency relationships
- **Build tools**: Provides structure for optimization
- **Version control**: Facilitates organized development workflow

## Customization

The planner can be customized for:
- Different size thresholds for categorization
- Custom naming conventions
- Framework-specific patterns (React, Vue, Angular)
- Project-specific requirements

## Best Practices

1. **Start with largest files**: Focus on performance-critical files first
2. **Maintain consistency**: Follow established patterns throughout
3. **Document decisions**: Keep rationale for structure choices
4. **Gradual migration**: Implement changes incrementally
5. **Update tooling**: Ensure build tools support new structure

This Module Planner Agent provides a data-driven approach to project organization, using actual file sizes and analysis to drive architectural decisions rather than arbitrary conventions.
