#!/bin/bash
# File Auditor Agent - Bash Implementation
# Task: List all .html, .css, .js files and record their sizes & locations.

echo "File Auditor Agent - Scanning for web files..."
echo "================================================"

# Create the TSV file using find command
echo "Generating audit.tsv..."
find . -type f \( -name "*.html" -o -name "*.css" -o -name "*.js" \) -printf '%P\t%s bytes\n' > audit.tsv

# Check if the file was created successfully
if [ -f "audit.tsv" ]; then
    echo "✓ audit.tsv created successfully"
    echo "File contains $(wc -l < audit.tsv) entries"
else
    echo "✗ Failed to create audit.tsv"
    exit 1
fi

# Display first few lines as preview
echo ""
echo "Preview of audit.tsv (first 10 lines):"
echo "======================================="
head -10 audit.tsv

echo ""
echo "Summary by file type:"
echo "===================="

# Count files by extension
html_count=$(grep -c '\.html' audit.tsv)
css_count=$(grep -c '\.css' audit.tsv)
js_count=$(grep -c '\.js' audit.tsv)

echo "HTML files: $html_count"
echo "CSS files:  $css_count"
echo "JS files:   $js_count"
echo "Total:      $((html_count + css_count + js_count))"

echo ""
echo "Largest files by type:"
echo "====================="

# Find largest files by type
echo "Largest HTML file:"
grep '\.html' audit.tsv | sort -k2 -nr | head -1

echo "Largest CSS file:"
grep '\.css' audit.tsv | sort -k2 -nr | head -1

echo "Largest JS file:"
grep '\.js' audit.tsv | sort -k2 -nr | head -1

echo ""
echo "Audit complete! Check audit.tsv for full results."
