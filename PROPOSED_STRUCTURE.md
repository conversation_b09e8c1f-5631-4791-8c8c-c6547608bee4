# Module Planner Agent - Proposed Target Structure

Based on the file audit analysis, here's the proposed optimized project structure using largest files to drive the "views" vs "components" split.

## Analysis Summary

**Current Project Stats:**
- Total files: 161 web files
- Largest HTML file: grind.html (201,061 bytes) → **VIEW**
- Largest CSS file: grind.css (125,643 bytes) → **VIEW STYLE**  
- Largest JS file: ai-researcher.js (101,375 bytes) → **VIEW CONTROLLER**

**Size-Based Categorization Rules:**
- HTML files >15KB = **VIEWS** (full pages)
- HTML files <15KB = **COMPONENTS** (reusable parts)
- JS files >30KB = **VIEW CONTROLLERS** (major features)
- JS files <30KB = **MODULES/COMPONENTS** (utilities/services)
- CSS files >20KB = **VIEW STYLES** (page-specific)
- CSS files <20KB = **COMPONENT STYLES** (reusable)

## Proposed Target Structure

```
/src
  /components          # Reusable UI components (<15KB HTML, <30KB JS)
    /ui                # Basic UI elements
      ├── sideDrawer.js/css (15KB CSS, 8KB JS)
      ├── clock-display.js (2KB)
      ├── ui-utilities.js (5KB)
      ├── theme-manager.js (2KB)
      └── notification.css (1KB)
    
    /forms             # Form components
      ├── task-notes.js/css (25KB JS, 10KB CSS)
      ├── text-expansion.js/css (26KB JS, 6KB CSS)
      ├── task-display.css (1KB)
      └── settings.css (7KB)
    
    /widgets           # Interactive widgets
      ├── energyLevels.js (2KB)
      ├── quoteManager.js (4KB)
      ├── soundManager.js (3KB)
      ├── energyHologram.js (9KB)
      └── pomodoroGlobal.js (10KB)
    
    /modals            # Modal dialogs and overlays
      ├── compact-style.css (4KB)
      ├── simulation-enhancer.css (4KB)
      └── priority-calculator.html (7KB)

  /views               # Main application views/pages (>15KB HTML, >30KB JS)
    /pages             # Full page views (large HTML files)
      ├── grind.html (201KB) ★ LARGEST
      ├── extracted.html (92KB)
      ├── landing.html (55KB)
      ├── academic-details.html (26KB)
      ├── workspace.html (21KB)
      ├── tasks.html (24KB)
      ├── instant-test-feedback.html (23KB)
      ├── study-spaces.html (21KB)
      └── flashcards.html (17KB)
    
    /controllers       # View controllers (>30KB JS files)
      ├── ai-researcher.js (101KB) ★ LARGEST JS
      ├── speech-recognition.js (81KB)
      ├── googleDriveApi.js (72KB)
      ├── semester-management.js (62KB)
      ├── studySpacesManager.js (57KB)
      ├── test-feedback.js (53KB)
      ├── flashcards.js (52KB)
      ├── flashcardManager.js (50KB)
      ├── calendarManager.js (43KB)
      ├── pomodoroTimer.js (35KB)
      ├── grind-speech-synthesis.js (34KB)
      └── calendar-views.js (33KB)

  /services            # Business logic and API services
    /api               # API communication
      ├── gemini-api.js (4KB)
      ├── googleGenerativeAI.js (1KB)
      ├── api-optimization.js (17KB)
      └── api-settings.js (7KB)
    
    /auth              # Authentication services
      ├── auth.js (11KB)
      ├── firebaseAuth.js (2KB)
      ├── firebase-config.js (3KB)
      └── firebase-init.js (3KB)
    
    /data              # Data management
      ├── firestore.js (21KB)
      ├── storageManager.js (2KB)
      ├── data-sync-manager.js (7KB)
      ├── data-loader.js (0KB)
      └── indexedDB.js (0KB)
    
    /integrations      # External service integrations
      ├── todoistIntegration.js (30KB)
      ├── cross-tab-sync.js (11KB)
      ├── data-sync-integration.js (2KB)
      └── flashcardTaskIntegration.js (28KB)

  /utils               # Utility functions and helpers
    /helpers           # General helper functions
      ├── common.js (2KB)
      ├── ui-utilities.js (5KB)
      ├── transitionManager.js (3KB)
      └── inject-header.js (2KB)
    
    /managers          # Specialized managers
      ├── tasksManager.js (9KB)
      ├── scheduleManager.js (5KB)
      ├── currentTaskManager.js (12KB)
      ├── sleepScheduleManager.js (3KB)
      ├── roleModelManager.js (11KB)
      └── recipeManager.js (20KB)

  /styles              # Organized stylesheets
    /base              # Base styles and variables
      ├── main.css (16KB)
      └── styles/main.css (16KB)
    
    /views             # View-specific styles (>20KB CSS)
      ├── grind.css (126KB) ★ LARGEST CSS
      ├── daily-calendar.css (50KB)
      ├── extracted.css (40KB)
      ├── workspace.css (40KB)
      ├── academic-details.css (27KB)
      └── study-spaces.css (19KB)
    
    /components        # Component-specific styles
      ├── sideDrawer.css (16KB)
      ├── sleep-saboteurs.css (17KB)
      ├── test-feedback.css (16KB)
      ├── ai-search-response.css (12KB)
      ├── alarm-service.css (11KB)
      ├── priority-list.css (10KB)
      ├── task-notes.css (10KB)
      ├── taskLinks.css (10KB)
      └── priority-calculator.css (8KB)
    
    /layouts           # Layout and structural styles
      ├── calendar.css (9KB)
      ├── tasks.css (6KB)
      ├── settings.css (7KB)
      └── flashcards.css (5KB)

  /assets              # Static assets
    /images            # Image files
      └── gpace-logo-white.png
    /sounds            # Audio files
      ├── notification.mp3
      ├── pop.mp3
      ├── alarm1.mp3
      ├── alarm2.mp3
      └── alarm3.mp3
    /icons             # Icon files
    /fonts             # Font files

/public                # Publicly accessible files
  /js                  # Public JavaScript
    └── cacheManager.js (3KB)
  └── service-worker.js (1 byte)

/server                # Server-side code (maintain existing structure)
  /routes              # API routes
    └── subtasks.js (3KB)
  /controllers         # Route controllers
    ├── dataStorage.js (6KB)
    └── timetableHandler.js (1KB)
  └── server.js (25KB)

/workers               # Web workers and background scripts
  ├── imageAnalysis.js (7KB)
  ├── priority-calculator-with-worker.js (25KB)
  ├── worker.js (7KB)
  └── test-worker.js (3KB)

/config                # Configuration files
  ├── package.json
  ├── firebase.json
  └── environment configs

/data                  # Application data (maintain existing)
  /static              # Static data files
    ├── locations.json
    ├── schedule.json
    └── timetable.json
  /energy-logs         # Energy tracking data
  /uploads             # User uploads

/relaxed-mode          # Special mode (could be moved to views)
  ├── index.html (7KB)
  ├── script.js (12KB)
  └── style.css (11KB)
```

## Migration Strategy

### Phase 1: High Priority (Large Files - Performance Impact)
1. **grind.html** (201KB) + **grind.css** (126KB) → `src/views/pages/` + `src/styles/views/`
2. **ai-researcher.js** (101KB) → `src/views/controllers/`
3. **speech-recognition.js** (81KB) → `src/views/controllers/`
4. **googleDriveApi.js** (72KB) → `src/views/controllers/`

### Phase 2: Medium Priority (Functional Modules)
1. All *Manager.js files → `src/utils/managers/`
2. All *Service.js files → `src/services/`
3. All integration files → `src/services/integrations/`
4. Large view files (>20KB HTML) → `src/views/pages/`

### Phase 3: Low Priority (Components and Utilities)
1. Small JS utilities → `src/utils/helpers/`
2. Component CSS files → `src/styles/components/`
3. UI components → `src/components/ui/`

## Benefits of New Structure

1. **Clear Separation**: Views vs Components based on actual file sizes
2. **Performance Optimization**: Large files are clearly identified and organized
3. **Maintainability**: Related files are grouped together
4. **Scalability**: Structure supports future growth
5. **Team Collaboration**: Clear ownership boundaries
6. **Build Optimization**: Easier to implement code splitting and lazy loading
7. **Consistent Patterns**: Predictable file locations

## Key Insights from Analysis

- **grind.html** at 201KB is clearly the main application view
- **ai-researcher.js** at 101KB is a major feature requiring its own controller
- Most CSS files are component-specific and relatively small
- Clear distinction between large view controllers and small utility modules
- Current flat structure in `/js` and `/css` needs hierarchical organization

This structure follows modern web development best practices while being driven by actual file size analysis rather than arbitrary decisions.
