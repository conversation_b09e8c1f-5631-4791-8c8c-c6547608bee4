{"summary": {"total_js": 50, "total_css": 19, "external_js": 16, "local_js": 34, "external_css": 6, "local_css": 13}, "dependencies": {"js": ["/js/inject-header.js", "https://accounts.google.com/gsi/client", "https://apis.google.com/js/api.js", "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "https://cdn.jsdelivr.net/npm/chart.js", "https://cdn.jsdelivr.net/npm/es-module-shims", "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "https://cdn.quilljs.com/1.3.6/quill.min.js", "https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js", "https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js", "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js", "https://kit.fontawesome.com/51198d7b97.js", "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js", "js/ai-latex-conversion.js", "js/ai-researcher.js", "js/alarm-mini-display.js", "js/alarm-service.js", "js/cacheManager.js", "js/common.js", "js/cross-tab-sync.js", "js/currentTaskManager.js", "js/energyLevels.js", "js/firebase-init.js", "js/grind-speech-synthesis.js", "js/initFirestoreData.js", "js/pomodoroTimer.js", "js/sideDrawer.js", "js/sleepTimeCalculator.js", "js/sm2.js", "js/speech-recognition.js", "js/speech-synthesis.js", "js/storageManager.js", "js/task-notes-injector.js", "js/task-notes.js", "js/taskLinks.js", "js/text-expansion.js", "js/userGuidance.js", "js/workspace-attachments.js", "js/workspace-core.js", "js/workspace-document.js", "js/workspace-formatting.js", "js/workspace-media.js", "js/workspace-tables-links.js", "js/workspace-ui.js", "js/workspaceFlashcardIntegration.js", "priority-calculator.js"], "css": ["css/ai-search-response.css", "css/alarm-service.css", "css/extracted.css", "css/search-modal.css", "css/sideDrawer.css", "css/simulation-enhancer.css", "css/task-display.css", "css/task-notes.css", "css/taskLinks.css", "css/text-expansion.css", "css/workspace.css", "grind.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.quilljs.com/1.3.6/quill.snow.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap", "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap", "main.css"]}, "file_mapping": {"index.html": {"file": "index.html", "scripts": ["js/cacheManager.js", "js/cross-tab-sync.js", "js/alarm-service.js", "js/alarm-mini-display.js", "/js/inject-header.js"], "stylesheets": ["https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "css/alarm-service.css"]}, "grind.html": {"file": "grind.html", "scripts": ["https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js", "js/ai-latex-conversion.js", "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "js/cross-tab-sync.js", "js/initFirestoreData.js", "js/common.js", "js/userGuidance.js", "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js", "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js", "https://cdn.jsdelivr.net/npm/chart.js", "https://kit.fontawesome.com/51198d7b97.js", "https://cdn.jsdelivr.net/npm/es-module-shims", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js", "js/storageManager.js", "js/grind-speech-synthesis.js", "js/taskLinks.js", "js/currentTaskManager.js", "priority-calculator.js", "js/sleepTimeCalculator.js", "js/energyLevels.js", "js/sideDrawer.js", "js/pomodoroTimer.js", "js/task-notes-injector.js", "js/task-notes.js", "js/text-expansion.js", "js/cross-tab-sync.js", "js/initFirestoreData.js", "js/common.js", "js/ai-researcher.js", "js/firebase-init.js", "/js/inject-header.js"], "stylesheets": ["css/task-notes.css", "css/text-expansion.css", "css/search-modal.css", "main.css", "grind.css", "css/ai-search-response.css", "css/task-display.css", "css/sideDrawer.css", "css/taskLinks.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "css/simulation-enhancer.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"]}, "workspace.html": {"file": "workspace.html", "scripts": ["https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js", "https://cdn.quilljs.com/1.3.6/quill.min.js", "https://apis.google.com/js/api.js", "https://accounts.google.com/gsi/client", "js/speech-recognition.js", "js/speech-synthesis.js", "js/workspaceFlashcardIntegration.js", "js/cross-tab-sync.js", "js/workspace-ui.js", "js/workspace-formatting.js", "js/workspace-document.js", "js/workspace-media.js", "js/workspace-tables-links.js", "js/workspace-attachments.js", "js/workspace-core.js", "https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js", "js/sm2.js", "/js/inject-header.js"], "stylesheets": ["https://cdn.quilljs.com/1.3.6/quill.snow.css", "css/workspace.css", "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"]}, "extracted.html": {"file": "extracted.html", "scripts": ["js/sideDrawer.js", "js/cross-tab-sync.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "/js/inject-header.js"], "stylesheets": ["css/extracted.css", "css/sideDrawer.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"]}}}