404.html	2114 bytes
academic-details.html	26146 bytes
css/academic-details.css	27044 bytes
css/ai-search-response.css	12338 bytes
css/alarm-service.css	11193 bytes
css/compact-style.css	4220 bytes
css/daily-calendar.css	50181 bytes
css/extracted.css	39627 bytes
css/flashcards.css	5129 bytes
css/notification.css	967 bytes
css/priority-calculator.css	7623 bytes
css/priority-list.css	10003 bytes
css/settings.css	7412 bytes
css/sideDrawer.css	15934 bytes
css/simulation-enhancer.css	4281 bytes
css/sleep-saboteurs.css	17245 bytes
css/study-spaces.css	19318 bytes
css/subject-marks.css	4989 bytes
css/task-display.css	828 bytes
css/task-notes.css	9506 bytes
css/taskLinks.css	9748 bytes
css/test-feedback.css	16264 bytes
css/text-expansion.css	6288 bytes
css/workspace.css	40046 bytes
daily-calendar.html	10240 bytes
extracted.html	92348 bytes
flashcards.html	16728 bytes
grind.css	125643 bytes
grind.html	201061 bytes
index.html	2464 bytes
instant-test-feedback.html	23222 bytes
js/academic-details.js	2090 bytes
js/add-favicon.js	2888 bytes
js/ai-latex-conversion.js	746 bytes
js/ai-researcher.js	101375 bytes
js/alarm-data-service.js	9780 bytes
js/alarm-handler.js	2032 bytes
js/alarm-mini-display.js	3702 bytes
js/alarm-service-worker.js	4761 bytes
js/alarm-service.js	20505 bytes
js/api-optimization.js	17304 bytes
js/api-settings.js	6746 bytes
js/apiSettingsManager.js	5021 bytes
js/auth.js	11414 bytes
js/calendar-views.js	33294 bytes
js/calendarManager.js	43345 bytes
js/clock-display.js	2038 bytes
js/common-header.js	5780 bytes
js/common.js	1758 bytes
js/cross-tab-sync.js	10833 bytes
js/currentTaskManager.js	11858 bytes
js/data-loader.js	0 bytes
js/data-sync-integration.js	2027 bytes
js/data-sync-manager.js	6643 bytes
js/energyHologram.js	8670 bytes
js/energyLevels.js	1616 bytes
js/fileViewer.js	17164 bytes
js/firebase-config.js	3200 bytes
js/firebase-init.js	3040 bytes
js/firebaseAuth.js	2385 bytes
js/firebaseConfig.js	1398 bytes
js/firestore-global.js	1454 bytes
js/firestore.js	21209 bytes
js/flashcardManager.js	49955 bytes
js/flashcards.js	51674 bytes
js/flashcardTaskIntegration.js	27730 bytes
js/gemini-api.js	4024 bytes
js/googleDriveApi.js	72304 bytes
js/googleGenerativeAI.js	506 bytes
js/grind-speech-synthesis.js	34139 bytes
js/imageAnalyzer.js	8580 bytes
js/indexedDB.js	0 bytes
js/initFirestoreData.js	11030 bytes
js/inject-header.js	2290 bytes
js/markdown-converter.js	11518 bytes
js/marks-tracking.js	0 bytes
js/pandoc-fallback.js	2505 bytes
js/pomodoroGlobal.js	9682 bytes
js/pomodoroTimer.js	34711 bytes
js/priority-list-sorting.js	10551 bytes
js/priority-list-utils.js	26792 bytes
js/priority-sync-fix.js	4836 bytes
js/priority-worker-wrapper.js	3578 bytes
js/quoteManager.js	4317 bytes
js/recipeManager.js	19704 bytes
js/reorganize-scripts.js	4478 bytes
js/roleModelManager.js	10829 bytes
js/scheduleManager.js	5023 bytes
js/semester-management.js	62190 bytes
js/sideDrawer.js	7658 bytes
js/simulation-enhancer.js	20114 bytes
js/sleep-saboteurs-init.js	778 bytes
js/sleepScheduleManager.js	2568 bytes
js/sleepTimeCalculator.js	2346 bytes
js/sm2.js	4254 bytes
js/soundManager.js	3372 bytes
js/speech-recognition.js	81174 bytes
js/speech-synthesis.js	26156 bytes
js/storageManager.js	2014 bytes
js/studySpaceAnalyzer.js	4485 bytes
js/studySpacesFirestore.js	8971 bytes
js/studySpacesManager.js	57162 bytes
js/subject-management.js	23637 bytes
js/subject-marks-integration.js	1277 bytes
js/subject-marks-ui.js	25657 bytes
js/subject-marks.js	12652 bytes
js/task-notes-injector.js	6313 bytes
js/task-notes.js	25499 bytes
js/taskAttachments.js	26806 bytes
js/taskFilters.js	6515 bytes
js/taskLinks.js	23567 bytes
js/tasksManager.js	8706 bytes
js/test-feedback.js	53354 bytes
js/text-expansion.js	26396 bytes
js/theme-manager.js	1830 bytes
js/themeManager.js	3094 bytes
js/timetableAnalyzer.js	8930 bytes
js/timetableIntegration.js	1255 bytes
js/todoistIntegration.js	30152 bytes
js/transitionManager.js	3222 bytes
js/ui-utilities.js	5298 bytes
js/update-html-files.js	1614 bytes
js/userGuidance.js	20953 bytes
js/weightage-connector.js	9813 bytes
js/workspace-attachments.js	24543 bytes
js/workspace-core.js	12221 bytes
js/workspace-document.js	14191 bytes
js/workspace-formatting.js	5203 bytes
js/workspace-media.js	9721 bytes
js/workspace-tables-links.js	10969 bytes
js/workspace-ui.js	4594 bytes
js/workspaceFlashcardIntegration.js	25930 bytes
landing.html	55202 bytes
priority-calculator-with-worker.js	25335 bytes
priority-calculator.html	7020 bytes
priority-calculator.js	26330 bytes
priority-list.html	5227 bytes
public/js/cacheManager.js	3212 bytes
public/service-worker.js	1 bytes
relaxed-mode/index.html	7111 bytes
relaxed-mode/script.js	11649 bytes
relaxed-mode/style.css	10525 bytes
scripts/theme.js	1019 bytes
server/dataStorage.js	6407 bytes
server/routes/subtasks.js	2736 bytes
server/timetableHandler.js	672 bytes
server.js	25361 bytes
settings.html	5577 bytes
sleep-saboteurs.html	7732 bytes
study-spaces.html	21065 bytes
styles/calendar.css	9141 bytes
styles/index.css	414 bytes
styles/main.css	15630 bytes
styles/study-spaces.css	3850 bytes
styles/tasks.css	6469 bytes
subject-marks.html	8598 bytes
tasks.html	24458 bytes
test-worker.js	3455 bytes
worker.js	6619 bytes
workers/imageAnalysis.js	7398 bytes
workspace.html	20606 bytes
