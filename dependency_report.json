{"scan_timestamp": "2025-06-08T17:11:10.047456", "root_directory": "E:\\Improving GPAce\\Creating an App", "summary": {"html_files_scanned": 18, "total_js_dependencies": 99, "total_css_dependencies": 32, "external_dependencies": 29, "local_dependencies": 102}, "dependencies": {"javascript": ["https://accounts.google.com/gsi/client", "https://apis.google.com/js/api.js", "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "https://cdn.jsdelivr.net/npm/chart.js", "https://cdn.jsdelivr.net/npm/es-module-shims", "https://cdn.jsdelivr.net/npm/marked/marked.min.js", "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "https://cdn.quilljs.com/1.3.6/quill.min.js", "https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js", "https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js", "https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js", "https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js", "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js", "https://kit.fontawesome.com/51198d7b97.js", "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js", "https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js", "https://www.gstatic.com/firebasejs/8.10.0/firebase-functions.js", "js/academic-details.js", "js/ai-latex-conversion.js", "js/ai-researcher.js", "js/alarm-mini-display.js", "js/alarm-service.js", "js/api-optimization.js", "js/api-settings.js", "js/apiSettingsManager.js", "js/app.js", "js/auth.js", "js/cacheManager.js", "js/calendarManager.js", "js/clock-display.js", "js/common.js", "js/cross-tab-sync.js", "js/currentTaskManager.js", "js/data-sync-integration.js", "js/energyLevels.js", "js/firebase-init.js", "js/firebaseAuth.js", "js/firestore-global.js", "js/firestore.js", "js/flashcards.js", "js/gemini-api.js", "js/grind-speech-synthesis.js", "js/imageAnalyzer.js", "js/initFirestoreData.js", "js/inject-header.js", "js/pomodoroTimer.js", "js/priority-list-sorting.js", "js/priority-list-utils.js", "js/priority-sync-fix.js", "js/quoteManager.js", "js/roleModelManager.js", "js/scheduleManager.js", "js/semester-management.js", "js/sideDrawer.js", "js/sleep-saboteurs-init.js", "js/sleepScheduleManager.js", "js/sleepTimeCalculator.js", "js/sm2.js", "js/soundManager.js", "js/speech-recognition.js", "js/speech-synthesis.js", "js/storageManager.js", "js/studySpaceAnalyzer.js", "js/studySpacesManager.js", "js/subject-management.js", "js/subject-marks-integration.js", "js/subject-marks-ui.js", "js/task-notes-injector.js", "js/task-notes.js", "js/taskFilters.js", "js/taskLinks.js", "js/tasksManager.js", "js/test-feedback.js", "js/text-expansion.js", "js/theme-manager.js", "js/theme-toggle.js", "js/themeManager.js", "js/timetableAnalyzer.js", "js/timetableIntegration.js", "js/todoistIntegration.js", "js/transitionManager.js", "js/ui-utilities.js", "js/userGuidance.js", "js/workspace-attachments.js", "js/workspace-core.js", "js/workspace-document.js", "js/workspace-formatting.js", "js/workspace-media.js", "js/workspace-tables-links.js", "js/workspace-ui.js", "js/workspaceFlashcardIntegration.js", "priority-calculator.js", "relaxed-mode/script.js", "socket.io/socket.io.js"], "css": ["css/academic-details.css", "css/ai-search-response.css", "css/alarm-service.css", "css/daily-calendar.css", "css/extracted.css", "css/flashcards.css", "css/priority-calculator.css", "css/priority-list.css", "css/search-modal.css", "css/settings.css", "css/sideDrawer.css", "css/simulation-enhancer.css", "css/sleep-saboteurs.css", "css/study-spaces.css", "css/subject-marks.css", "css/task-display.css", "css/task-notes.css", "css/taskLinks.css", "css/test-feedback.css", "css/text-expansion.css", "css/workspace.css", "grind.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.quilljs.com/1.3.6/quill.snow.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap", "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap", "main.css", "relaxed-mode/style.css", "styles/main.css"], "external": ["https://accounts.google.com/gsi/client", "https://apis.google.com/js/api.js", "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "https://cdn.jsdelivr.net/npm/chart.js", "https://cdn.jsdelivr.net/npm/es-module-shims", "https://cdn.jsdelivr.net/npm/marked/marked.min.js", "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "https://cdn.quilljs.com/1.3.6/quill.min.js", "https://cdn.quilljs.com/1.3.6/quill.snow.css", "https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js", "https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js", "https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js", "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js", "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap", "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap", "https://kit.fontawesome.com/51198d7b97.js", "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js", "https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js", "https://www.gstatic.com/firebasejs/8.10.0/firebase-functions.js"], "local": ["css/academic-details.css", "css/ai-search-response.css", "css/alarm-service.css", "css/daily-calendar.css", "css/extracted.css", "css/flashcards.css", "css/priority-calculator.css", "css/priority-list.css", "css/search-modal.css", "css/settings.css", "css/sideDrawer.css", "css/simulation-enhancer.css", "css/sleep-saboteurs.css", "css/study-spaces.css", "css/subject-marks.css", "css/task-display.css", "css/task-notes.css", "css/taskLinks.css", "css/test-feedback.css", "css/text-expansion.css", "css/workspace.css", "grind.css", "js/academic-details.js", "js/ai-latex-conversion.js", "js/ai-researcher.js", "js/alarm-mini-display.js", "js/alarm-service.js", "js/api-optimization.js", "js/api-settings.js", "js/apiSettingsManager.js", "js/app.js", "js/auth.js", "js/cacheManager.js", "js/calendarManager.js", "js/clock-display.js", "js/common.js", "js/cross-tab-sync.js", "js/currentTaskManager.js", "js/data-sync-integration.js", "js/energyLevels.js", "js/firebase-init.js", "js/firebaseAuth.js", "js/firestore-global.js", "js/firestore.js", "js/flashcards.js", "js/gemini-api.js", "js/grind-speech-synthesis.js", "js/imageAnalyzer.js", "js/initFirestoreData.js", "js/inject-header.js", "js/pomodoroTimer.js", "js/priority-list-sorting.js", "js/priority-list-utils.js", "js/priority-sync-fix.js", "js/quoteManager.js", "js/roleModelManager.js", "js/scheduleManager.js", "js/semester-management.js", "js/sideDrawer.js", "js/sleep-saboteurs-init.js", "js/sleepScheduleManager.js", "js/sleepTimeCalculator.js", "js/sm2.js", "js/soundManager.js", "js/speech-recognition.js", "js/speech-synthesis.js", "js/storageManager.js", "js/studySpaceAnalyzer.js", "js/studySpacesManager.js", "js/subject-management.js", "js/subject-marks-integration.js", "js/subject-marks-ui.js", "js/task-notes-injector.js", "js/task-notes.js", "js/taskFilters.js", "js/taskLinks.js", "js/tasksManager.js", "js/test-feedback.js", "js/text-expansion.js", "js/theme-manager.js", "js/theme-toggle.js", "js/themeManager.js", "js/timetableAnalyzer.js", "js/timetableIntegration.js", "js/todoistIntegration.js", "js/transitionManager.js", "js/ui-utilities.js", "js/userGuidance.js", "js/workspace-attachments.js", "js/workspace-core.js", "js/workspace-document.js", "js/workspace-formatting.js", "js/workspace-media.js", "js/workspace-tables-links.js", "js/workspace-ui.js", "js/workspaceFlashcardIntegration.js", "main.css", "priority-calculator.js", "relaxed-mode/script.js", "relaxed-mode/style.css", "socket.io/socket.io.js", "styles/main.css"]}, "file_mapping": {"404.html": {"scripts": [{"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}], "stylesheets": [], "script_count": 3, "stylesheet_count": 0}, "academic-details.html": {"scripts": [{"src": "js/academic-details.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/academic-details.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "js/subject-management.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/subject-management.js\">"}, {"src": "js/ui-utilities.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/ui-utilities.js\">"}, {"src": "js/semester-management.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/semester-management.js\">"}, {"src": "js/auth.js", "type": "module", "async": false, "defer": false, "full_tag": "<script src=\"js/auth.js\" type=\"module\">"}, {"src": "js/firestore.js", "type": "module", "async": false, "defer": false, "full_tag": "<script src=\"js/firestore.js\" type=\"module\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/academic-details.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/academic-details.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/subject-management.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/subject-management.js\">"}, {"src": "js/ui-utilities.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/ui-utilities.js\">"}, {"src": "js/semester-management.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/semester-management.js\">"}, {"src": "js/academic-details.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/academic-details.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "js/subject-management.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/subject-management.js\">"}, {"src": "js/ui-utilities.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/ui-utilities.js\">"}, {"src": "js/semester-management.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/semester-management.js\">"}, {"src": "js/auth.js", "type": "module", "async": false, "defer": false, "full_tag": "<script src=\"js/auth.js\" type=\"module\">"}, {"src": "js/firestore.js", "type": "module", "async": false, "defer": false, "full_tag": "<script src=\"js/firestore.js\" type=\"module\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}, {"href": "css/academic-details.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/academic-details.css\" rel=\"stylesheet\">"}], "script_count": 25, "stylesheet_count": 4}, "daily-calendar.html": {"scripts": [{"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "js/firebaseAuth.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebaseAuth.js\">"}, {"src": "js/sleepScheduleManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sleepScheduleManager.js\">"}, {"src": "js/calendarManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/calendarManager.js\">"}, {"src": "js/timetableIntegration.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/timetableIntegration.js\">"}, {"src": "js/currentTaskManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/currentTaskManager.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/firebaseAuth.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebaseAuth.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "js/firebaseAuth.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebaseAuth.js\">"}, {"src": "js/sleepScheduleManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sleepScheduleManager.js\">"}, {"src": "js/calendarManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/calendarManager.js\">"}, {"src": "js/timetableIntegration.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/timetableIntegration.js\">"}, {"src": "js/currentTaskManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/currentTaskManager.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "main.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link type=\"text/css\" rel=\"stylesheet\" href=\"main.css\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}, {"href": "css/daily-calendar.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/daily-calendar.css\" rel=\"stylesheet\">"}, {"href": "css/task-display.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/task-display.css\" rel=\"stylesheet\">"}], "script_count": 18, "stylesheet_count": 7}, "extracted.html": {"scripts": [{"src": "js/sideDrawer.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/sideDrawer.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/sideDrawer.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/sideDrawer.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/sideDrawer.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/sideDrawer.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}, {"href": "css/extracted.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/extracted.css\" rel=\"stylesheet\">"}], "script_count": 10, "stylesheet_count": 5}, "flashcards.html": {"scripts": [{"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/storageManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/storageManager.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/sm2.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sm2.js\">"}, {"src": "js/flashcards.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/flashcards.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/storageManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/storageManager.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/sm2.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sm2.js\">"}, {"src": "js/flashcards.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/flashcards.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "main.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link type=\"text/css\" rel=\"stylesheet\" href=\"main.css\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}, {"href": "grind.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"grind.css\" rel=\"stylesheet\">"}, {"href": "css/flashcards.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/flashcards.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" as=\"style\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" as=\"style\">"}, {"href": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\" as=\"style\">"}, {"href": "main.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"main.css\" as=\"style\">"}, {"href": "css/sideDrawer.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"css/sideDrawer.css\" as=\"style\">"}], "script_count": 18, "stylesheet_count": 12}, "grind.html": {"scripts": [{"src": "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js\">"}, {"src": "js/ai-latex-conversion.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/ai-latex-conversion.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "type": "text/javascript", "async": true, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js\" id=\"MathJax-script\" async>"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/initFirestoreData.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/initFirestoreData.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/userGuidance.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/userGuidance.js\">"}, {"src": "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js", "type": "text/javascript", "async": true, "defer": false, "full_tag": "<script async src=\"https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js\">"}, {"src": "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/chart.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdn.jsdelivr.net/npm/chart.js\">"}, {"src": "https://kit.fontawesome.com/51198d7b97.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://kit.fontawesome.com/51198d7b97.js\" crossorigin=\"anonymous\">"}, {"src": "https://cdn.jsdelivr.net/npm/es-module-shims", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdn.jsdelivr.net/npm/es-module-shims\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/storageManager.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/storageManager.js\">"}, {"src": "js/grind-speech-synthesis.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/grind-speech-synthesis.js\">"}, {"src": "js/taskLinks.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/taskLinks.js\">"}, {"src": "js/currentTaskManager.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/currentTaskManager.js\">"}, {"src": "priority-calculator.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"priority-calculator.js\">"}, {"src": "js/sleepTimeCalculator.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/sleepTimeCalculator.js\">"}, {"src": "js/energyLevels.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/energyLevels.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/sideDrawer.js\">"}, {"src": "js/pomodoroTimer.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/pomodoroTimer.js\">"}, {"src": "js/task-notes-injector.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/task-notes-injector.js\">"}, {"src": "js/task-notes.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/task-notes.js\">"}, {"src": "js/text-expansion.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/text-expansion.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/initFirestoreData.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/initFirestoreData.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/ai-researcher.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/ai-researcher.js\">"}, {"src": "js/firebase-init.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebase-init.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"/js/inject-header.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/initFirestoreData.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/initFirestoreData.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/userGuidance.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/userGuidance.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/initFirestoreData.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/initFirestoreData.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/ai-researcher.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/ai-researcher.js\">"}, {"src": "js/firebase-init.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebase-init.js\">"}, {"src": "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js\">"}, {"src": "js/ai-latex-conversion.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/ai-latex-conversion.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "type": "text/javascript", "async": true, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js\" id=\"MathJax-script\" async>"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/initFirestoreData.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/initFirestoreData.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/userGuidance.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/userGuidance.js\">"}, {"src": "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js", "type": "text/javascript", "async": true, "defer": false, "full_tag": "<script async src=\"https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js\">"}, {"src": "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/chart.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdn.jsdelivr.net/npm/chart.js\">"}, {"src": "https://kit.fontawesome.com/51198d7b97.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://kit.fontawesome.com/51198d7b97.js\" crossorigin=\"anonymous\">"}, {"src": "https://cdn.jsdelivr.net/npm/es-module-shims", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdn.jsdelivr.net/npm/es-module-shims\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/storageManager.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/storageManager.js\">"}, {"src": "js/grind-speech-synthesis.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/grind-speech-synthesis.js\">"}, {"src": "js/taskLinks.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/taskLinks.js\">"}, {"src": "js/currentTaskManager.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/currentTaskManager.js\">"}, {"src": "priority-calculator.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"priority-calculator.js\">"}, {"src": "js/sleepTimeCalculator.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/sleepTimeCalculator.js\">"}, {"src": "js/energyLevels.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/energyLevels.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/sideDrawer.js\">"}, {"src": "js/pomodoroTimer.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/pomodoroTimer.js\">"}, {"src": "js/task-notes-injector.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/task-notes-injector.js\">"}, {"src": "js/task-notes.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"js/task-notes.js\">"}, {"src": "js/text-expansion.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/text-expansion.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/initFirestoreData.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/initFirestoreData.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/ai-researcher.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/ai-researcher.js\">"}, {"src": "js/firebase-init.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebase-init.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script defer src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "css/taskLinks.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link rel=\"stylesheet\" href=\"css/taskLinks.css\">"}, {"href": "css/search-modal.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link rel=\"stylesheet\" href=\"css/search-modal.css\">"}, {"href": "main.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link type=\"text/css\" rel=\"stylesheet\" href=\"main.css\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}, {"href": "grind.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"grind.css\" rel=\"stylesheet\">"}, {"href": "css/task-display.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/task-display.css\" rel=\"stylesheet\">"}, {"href": "css/text-expansion.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/text-expansion.css\" rel=\"stylesheet\">"}, {"href": "css/simulation-enhancer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/simulation-enhancer.css\" rel=\"stylesheet\">"}, {"href": "css/ai-search-response.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/ai-search-response.css\" rel=\"stylesheet\">"}, {"href": "css/task-notes.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/task-notes.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" as=\"style\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" as=\"style\">"}, {"href": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\" as=\"style\">"}, {"href": "main.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"main.css\" as=\"style\">"}, {"href": "css/sideDrawer.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"css/sideDrawer.css\" as=\"style\">"}], "script_count": 77, "stylesheet_count": 18}, "index.html": {"scripts": [{"src": "js/cacheManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/cacheManager.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/alarm-service.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/alarm-service.js\">"}, {"src": "js/alarm-mini-display.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/alarm-mini-display.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/cacheManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/cacheManager.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/alarm-service.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/alarm-service.js\">"}, {"src": "js/alarm-mini-display.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/alarm-mini-display.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "css/alarm-service.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/alarm-service.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" as=\"style\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" as=\"style\">"}], "script_count": 11, "stylesheet_count": 3}, "instant-test-feedback.html": {"scripts": [{"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/marked/marked.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/marked/marked.min.js\">"}, {"src": "https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js\">"}, {"src": "https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js\">"}, {"src": "js/api-settings.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/api-settings.js\">"}, {"src": "js/api-optimization.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/api-optimization.js\">"}, {"src": "js/test-feedback.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/test-feedback.js\">"}, {"src": "js/gemini-api.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/gemini-api.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/marked/marked.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/marked/marked.min.js\">"}, {"src": "https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js\">"}, {"src": "https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js\">"}, {"src": "js/api-settings.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/api-settings.js\">"}, {"src": "js/api-optimization.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/api-optimization.js\">"}, {"src": "js/test-feedback.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/test-feedback.js\">"}, {"src": "js/gemini-api.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/gemini-api.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "main.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link type=\"text/css\" rel=\"stylesheet\" href=\"main.css\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}, {"href": "grind.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"grind.css\" rel=\"stylesheet\">"}, {"href": "css/test-feedback.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/test-feedback.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" as=\"style\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" as=\"style\">"}, {"href": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css\" as=\"style\">"}, {"href": "main.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"main.css\" as=\"style\">"}, {"href": "css/sideDrawer.css", "rel": "preload", "type": "text/css", "media": "all", "full_tag": "<link rel=\"preload\" href=\"css/sideDrawer.css\" as=\"style\">"}], "script_count": 23, "stylesheet_count": 12}, "landing.html": {"scripts": [{"src": "js/theme-toggle.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/theme-toggle.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/theme-toggle.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/theme-toggle.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "styles/main.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link type=\"text/css\" rel=\"stylesheet\" href=\"styles/main.css\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}], "script_count": 7, "stylesheet_count": 4}, "priority-calculator.html": {"scripts": [{"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "priority-calculator.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"priority-calculator.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "priority-calculator.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"priority-calculator.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}], "stylesheets": [{"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}, {"href": "css/priority-calculator.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/priority-calculator.css\" rel=\"stylesheet\">"}], "script_count": 12, "stylesheet_count": 4}, "priority-list.html": {"scripts": [{"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/priority-sync-fix.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/priority-sync-fix.js\">"}, {"src": "js/priority-list-sorting.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/priority-list-sorting.js\">"}, {"src": "js/priority-list-utils.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/priority-list-utils.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/priority-sync-fix.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/priority-sync-fix.js\">"}, {"src": "js/priority-list-utils.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/priority-list-utils.js\">"}, {"src": "js/common.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/common.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/priority-sync-fix.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/priority-sync-fix.js\">"}, {"src": "js/priority-list-sorting.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/priority-list-sorting.js\">"}, {"src": "js/priority-list-utils.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/priority-list-utils.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}, {"href": "css/priority-list.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/priority-list.css\" rel=\"stylesheet\">"}], "script_count": 18, "stylesheet_count": 4}, "relaxed-mode\\index.html": {"scripts": [{"src": "../js/common.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script src=\"../js/common.js\" defer>"}, {"src": "../js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"../js/cross-tab-sync.js\">"}, {"src": "../js/tasksManager.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script src=\"../js/tasksManager.js\" defer>"}, {"src": "../js/transitionManager.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script src=\"../js/transitionManager.js\" defer>"}, {"src": "../js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"../js/sideDrawer.js\">"}, {"src": "script.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script src=\"script.js\" defer>"}, {"src": "../js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"../js/cross-tab-sync.js\">"}, {"src": "../js/common.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script src=\"../js/common.js\" defer>"}, {"src": "../js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"../js/cross-tab-sync.js\">"}, {"src": "../js/tasksManager.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script src=\"../js/tasksManager.js\" defer>"}, {"src": "../js/transitionManager.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script src=\"../js/transitionManager.js\" defer>"}, {"src": "../js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"../js/sideDrawer.js\">"}, {"src": "script.js", "type": "text/javascript", "async": false, "defer": true, "full_tag": "<script src=\"script.js\" defer>"}], "stylesheets": [{"href": "../styles/main.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link rel=\"stylesheet\" href=\"../styles/main.css\">"}, {"href": "style.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link rel=\"stylesheet\" href=\"style.css\">"}, {"href": "../css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link rel=\"stylesheet\" href=\"../css/sideDrawer.css\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}], "script_count": 13, "stylesheet_count": 4}, "settings.html": {"scripts": [{"src": "js/soundManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/soundManager.js\">"}, {"src": "js/transitionManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/transitionManager.js\">"}, {"src": "js/todoistIntegration.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/todoistIntegration.js\">"}, {"src": "js/app.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/app.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js\">"}, {"src": "js/gemini-api.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/gemini-api.js\">"}, {"src": "js/themeManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/themeManager.js\">"}, {"src": "js/quoteManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/quoteManager.js\">"}, {"src": "js/roleModelManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/roleModelManager.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/soundManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/soundManager.js\">"}, {"src": "js/transitionManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/transitionManager.js\">"}, {"src": "js/todoistIntegration.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/todoistIntegration.js\">"}, {"src": "js/app.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/app.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js\">"}, {"src": "js/gemini-api.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/gemini-api.js\">"}, {"src": "js/themeManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/themeManager.js\">"}, {"src": "js/quoteManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/quoteManager.js\">"}, {"src": "js/roleModelManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/roleModelManager.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "css/settings.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/settings.css\" rel=\"stylesheet\">"}], "script_count": 22, "stylesheet_count": 3}, "sleep-saboteurs.html": {"scripts": [{"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/alarm-service.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/alarm-service.js\">"}, {"src": "js/clock-display.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/clock-display.js\">"}, {"src": "js/theme-manager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/theme-manager.js\">"}, {"src": "js/sleep-saboteurs-init.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sleep-saboteurs-init.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/alarm-service.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/alarm-service.js\">"}, {"src": "js/clock-display.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/clock-display.js\">"}, {"src": "js/theme-manager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/theme-manager.js\">"}, {"src": "js/sleep-saboteurs-init.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sleep-saboteurs-init.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}, {"href": "css/alarm-service.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/alarm-service.css\" rel=\"stylesheet\">"}, {"href": "css/sleep-saboteurs.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sleep-saboteurs.css\" rel=\"stylesheet\">"}], "script_count": 17, "stylesheet_count": 5}, "study-spaces.html": {"scripts": [{"src": "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "/socket.io/socket.io.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/socket.io/socket.io.js\">"}, {"src": "js/timetableAnalyzer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/timetableAnalyzer.js\">"}, {"src": "js/studySpacesManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/studySpacesManager.js\">"}, {"src": "js/imageAnalyzer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/imageAnalyzer.js\">"}, {"src": "js/scheduleManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/scheduleManager.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js\">"}, {"src": "https://www.gstatic.com/firebasejs/8.10.0/firebase-functions.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://www.gstatic.com/firebasejs/8.10.0/firebase-functions.js\">"}, {"src": "js/firebaseAuth.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebaseAuth.js\">"}, {"src": "js/apiSettingsManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/apiSettingsManager.js\">"}, {"src": "js/studySpaceAnalyzer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/studySpaceAnalyzer.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/firebaseAuth.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebaseAuth.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "/socket.io/socket.io.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/socket.io/socket.io.js\">"}, {"src": "js/timetableAnalyzer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/timetableAnalyzer.js\">"}, {"src": "js/studySpacesManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/studySpacesManager.js\">"}, {"src": "js/imageAnalyzer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/imageAnalyzer.js\">"}, {"src": "js/scheduleManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/scheduleManager.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js\">"}, {"src": "https://www.gstatic.com/firebasejs/8.10.0/firebase-functions.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://www.gstatic.com/firebasejs/8.10.0/firebase-functions.js\">"}, {"src": "js/firebaseAuth.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebaseAuth.js\">"}, {"src": "js/apiSettingsManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/apiSettingsManager.js\">"}, {"src": "js/studySpaceAnalyzer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/studySpaceAnalyzer.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "main.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link type=\"text/css\" rel=\"stylesheet\" href=\"main.css\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}, {"href": "css/study-spaces.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/study-spaces.css\" rel=\"stylesheet\">"}], "script_count": 32, "stylesheet_count": 5}, "subject-marks.html": {"scripts": [{"src": "js/firebase-init.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebase-init.js\">"}, {"src": "js/firestore-global.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firestore-global.js\">"}, {"src": "js/subject-marks-integration.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/subject-marks-integration.js\">"}, {"src": "js/data-sync-integration.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/data-sync-integration.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/subject-marks-ui.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/subject-marks-ui.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/firebase-init.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebase-init.js\">"}, {"src": "js/firestore-global.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firestore-global.js\">"}, {"src": "js/subject-marks-integration.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/subject-marks-integration.js\">"}, {"src": "js/data-sync-integration.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/data-sync-integration.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/subject-marks-ui.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/subject-marks-ui.js\">"}, {"src": "js/firebase-init.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firebase-init.js\">"}, {"src": "js/firestore-global.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/firestore-global.js\">"}, {"src": "js/subject-marks-integration.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/subject-marks-integration.js\">"}, {"src": "js/data-sync-integration.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/data-sync-integration.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/subject-marks-ui.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/subject-marks-ui.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}, {"href": "css/subject-marks.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/subject-marks.css\" rel=\"stylesheet\">"}], "script_count": 24, "stylesheet_count": 4}, "tasks.html": {"scripts": [{"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/todoistIntegration.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/todoistIntegration.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "js/tasksManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/tasksManager.js\">"}, {"src": "js/taskFilters.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/taskFilters.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js\">"}, {"src": "js/todoistIntegration.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/todoistIntegration.js\">"}, {"src": "js/sideDrawer.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sideDrawer.js\">"}, {"src": "js/tasksManager.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/tasksManager.js\">"}, {"src": "js/taskFilters.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/taskFilters.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "css/sideDrawer.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"css/sideDrawer.css\" rel=\"stylesheet\">"}], "script_count": 15, "stylesheet_count": 3}, "workspace.html": {"scripts": [{"src": "https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js\">"}, {"src": "https://cdn.quilljs.com/1.3.6/quill.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.quilljs.com/1.3.6/quill.min.js\">"}, {"src": "https://apis.google.com/js/api.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://apis.google.com/js/api.js\">"}, {"src": "https://accounts.google.com/gsi/client", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://accounts.google.com/gsi/client\">"}, {"src": "js/speech-recognition.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/speech-recognition.js\">"}, {"src": "js/speech-synthesis.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/speech-synthesis.js\">"}, {"src": "js/workspaceFlashcardIntegration.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/workspaceFlashcardIntegration.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/workspace-ui.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-ui.js\">"}, {"src": "js/workspace-formatting.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-formatting.js\">"}, {"src": "js/workspace-document.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-document.js\">"}, {"src": "js/workspace-media.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-media.js\">"}, {"src": "js/workspace-tables-links.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-tables-links.js\">"}, {"src": "js/workspace-attachments.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-attachments.js\">"}, {"src": "js/workspace-core.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-core.js\">"}, {"src": "https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js\">"}, {"src": "js/sm2.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sm2.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}, {"src": "js/workspaceFlashcardIntegration.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/workspaceFlashcardIntegration.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js\">"}, {"src": "https://cdn.quilljs.com/1.3.6/quill.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdn.quilljs.com/1.3.6/quill.min.js\">"}, {"src": "https://apis.google.com/js/api.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://apis.google.com/js/api.js\">"}, {"src": "https://accounts.google.com/gsi/client", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://accounts.google.com/gsi/client\">"}, {"src": "js/speech-recognition.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/speech-recognition.js\">"}, {"src": "js/speech-synthesis.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/speech-synthesis.js\">"}, {"src": "js/workspaceFlashcardIntegration.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/workspaceFlashcardIntegration.js\">"}, {"src": "js/cross-tab-sync.js", "type": "module", "async": false, "defer": false, "full_tag": "<script type=\"module\" src=\"js/cross-tab-sync.js\">"}, {"src": "js/workspace-ui.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-ui.js\">"}, {"src": "js/workspace-formatting.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-formatting.js\">"}, {"src": "js/workspace-document.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-document.js\">"}, {"src": "js/workspace-media.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-media.js\">"}, {"src": "js/workspace-tables-links.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-tables-links.js\">"}, {"src": "js/workspace-attachments.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-attachments.js\">"}, {"src": "js/workspace-core.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/workspace-core.js\">"}, {"src": "https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js\">"}, {"src": "js/sm2.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"js/sm2.js\">"}, {"src": "/js/inject-header.js", "type": "text/javascript", "async": false, "defer": false, "full_tag": "<script src=\"/js/inject-header.js\">"}], "stylesheets": [{"href": "css/workspace.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link rel=\"stylesheet\" href=\"css/workspace.css\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css\" rel=\"stylesheet\">"}, {"href": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css\" rel=\"stylesheet\">"}, {"href": "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap\" rel=\"stylesheet\">"}, {"href": "https://cdn.quilljs.com/1.3.6/quill.snow.css", "rel": "stylesheet", "type": "text/css", "media": "all", "full_tag": "<link href=\"https://cdn.quilljs.com/1.3.6/quill.snow.css\" rel=\"stylesheet\">"}], "script_count": 38, "stylesheet_count": 5}}}