#!/usr/bin/env python3
"""
Dependency Mapper Agent
Task: Extract <script> and <link> tags, map references.

This script provides comprehensive dependency mapping for web development projects.
"""

import os
import re
import json
import csv
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set, Tuple
import argparse
from urllib.parse import urlparse


class DependencyMapper:
    """Dependency Mapper Agent for extracting and analyzing web dependencies."""
    
    def __init__(self, root_dir: str = "."):
        self.root_dir = Path(root_dir).resolve()
        self.html_files = []
        self.js_dependencies = set()
        self.css_dependencies = set()
        self.external_dependencies = set()
        self.local_dependencies = set()
        self.dependency_map = {}
        
    def find_html_files(self) -> List[Path]:
        """Find all HTML files in the project."""
        html_files = []
        for html_file in self.root_dir.glob("**/*.html"):
            if html_file.is_file():
                html_files.append(html_file)
        
        self.html_files = sorted(html_files)
        return self.html_files
    
    def extract_script_tags(self, content: str) -> List[Dict]:
        """Extract script tags and their sources."""
        script_patterns = [
            # Standard script tags with src
            r'<script[^>]+src=["\']([^"\']+)["\'][^>]*>',
            # Module script tags
            r'<script[^>]+type=["\']module["\'][^>]+src=["\']([^"\']+)["\'][^>]*>',
            # Script tags with various attribute orders
            r'<script[^>]*src=["\']([^"\']+)["\'][^>]*>',
        ]
        
        scripts = []
        for pattern in script_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                src = match.group(1)
                full_match = match.group(0)
                
                # Extract additional attributes
                type_match = re.search(r'type=["\']([^"\']+)["\']', full_match, re.IGNORECASE)
                async_match = re.search(r'\basync\b', full_match, re.IGNORECASE)
                defer_match = re.search(r'\bdefer\b', full_match, re.IGNORECASE)
                
                script_info = {
                    'src': src,
                    'type': type_match.group(1) if type_match else 'text/javascript',
                    'async': bool(async_match),
                    'defer': bool(defer_match),
                    'full_tag': full_match.strip()
                }
                scripts.append(script_info)
        
        return scripts
    
    def extract_link_tags(self, content: str) -> List[Dict]:
        """Extract link tags for stylesheets."""
        link_patterns = [
            # Standard stylesheet links
            r'<link[^>]+rel=["\']stylesheet["\'][^>]+href=["\']([^"\']+)["\'][^>]*>',
            r'<link[^>]+href=["\']([^"\']+)["\'][^>]+rel=["\']stylesheet["\'][^>]*>',
            # Preload links for CSS
            r'<link[^>]+rel=["\']preload["\'][^>]+href=["\']([^"\']+\.css)["\'][^>]*>',
        ]
        
        links = []
        for pattern in link_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                href = match.group(1)
                full_match = match.group(0)
                
                # Extract additional attributes
                rel_match = re.search(r'rel=["\']([^"\']+)["\']', full_match, re.IGNORECASE)
                type_match = re.search(r'type=["\']([^"\']+)["\']', full_match, re.IGNORECASE)
                media_match = re.search(r'media=["\']([^"\']+)["\']', full_match, re.IGNORECASE)
                
                link_info = {
                    'href': href,
                    'rel': rel_match.group(1) if rel_match else 'stylesheet',
                    'type': type_match.group(1) if type_match else 'text/css',
                    'media': media_match.group(1) if media_match else 'all',
                    'full_tag': full_match.strip()
                }
                links.append(link_info)
        
        return links
    
    def is_external_url(self, url: str) -> bool:
        """Check if URL is external (has protocol and domain)."""
        parsed = urlparse(url)
        return bool(parsed.scheme and parsed.netloc)
    
    def normalize_path(self, path: str, html_file_path: Path) -> str:
        """Normalize relative paths to be consistent."""
        if self.is_external_url(path):
            return path
        
        # Handle absolute paths starting with /
        if path.startswith('/'):
            return path[1:]  # Remove leading slash for consistency
        
        # Handle relative paths
        html_dir = html_file_path.parent
        try:
            resolved_path = (html_dir / path).resolve()
            relative_to_root = resolved_path.relative_to(self.root_dir)
            return str(relative_to_root).replace('\\', '/')
        except (ValueError, OSError):
            # If path can't be resolved, return as-is
            return path
    
    def scan_dependencies(self) -> Dict:
        """Scan all HTML files for dependencies."""
        print(f"Scanning {len(self.html_files)} HTML files for dependencies...")
        
        for html_file in self.html_files:
            try:
                with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                relative_path = html_file.relative_to(self.root_dir)
                
                # Extract scripts and links
                scripts = self.extract_script_tags(content)
                links = self.extract_link_tags(content)
                
                # Process scripts
                for script in scripts:
                    src = script['src']
                    normalized_src = self.normalize_path(src, html_file)
                    
                    if self.is_external_url(src):
                        self.external_dependencies.add(src)
                    else:
                        self.local_dependencies.add(normalized_src)
                    
                    self.js_dependencies.add(normalized_src)
                
                # Process CSS links
                for link in links:
                    href = link['href']
                    normalized_href = self.normalize_path(href, html_file)
                    
                    if self.is_external_url(href):
                        self.external_dependencies.add(href)
                    else:
                        self.local_dependencies.add(normalized_href)
                    
                    self.css_dependencies.add(normalized_href)
                
                # Store file-specific dependencies
                self.dependency_map[str(relative_path)] = {
                    'scripts': scripts,
                    'stylesheets': links,
                    'script_count': len(scripts),
                    'stylesheet_count': len(links)
                }
                
            except Exception as e:
                print(f"Error processing {html_file}: {e}")
        
        return self.dependency_map
    
    def generate_simple_lists(self):
        """Generate simple text files as specified in the original requirement."""
        # JS dependencies
        with open('js_deps.txt', 'w', encoding='utf-8') as f:
            for dep in sorted(self.js_dependencies):
                f.write(f"{dep}\n")
        
        # CSS dependencies
        with open('css_deps.txt', 'w', encoding='utf-8') as f:
            for dep in sorted(self.css_dependencies):
                f.write(f"{dep}\n")
        
        print("Generated js_deps.txt and css_deps.txt")
    
    def generate_json_report(self, output_file: str = "dependency_report.json"):
        """Generate comprehensive JSON report."""
        report = {
            'scan_timestamp': datetime.now().isoformat(),
            'root_directory': str(self.root_dir),
            'summary': {
                'html_files_scanned': len(self.html_files),
                'total_js_dependencies': len(self.js_dependencies),
                'total_css_dependencies': len(self.css_dependencies),
                'external_dependencies': len(self.external_dependencies),
                'local_dependencies': len(self.local_dependencies)
            },
            'dependencies': {
                'javascript': sorted(list(self.js_dependencies)),
                'css': sorted(list(self.css_dependencies)),
                'external': sorted(list(self.external_dependencies)),
                'local': sorted(list(self.local_dependencies))
            },
            'file_mapping': self.dependency_map
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"Generated {output_file}")
    
    def generate_csv_report(self, output_file: str = "dependency_report.csv"):
        """Generate CSV report for spreadsheet analysis."""
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['html_file', 'dependency_type', 'dependency_path', 'is_external', 'tag_type', 'attributes'])
            
            for html_file, deps in self.dependency_map.items():
                # Write script dependencies
                for script in deps['scripts']:
                    writer.writerow([
                        html_file,
                        'javascript',
                        script['src'],
                        self.is_external_url(script['src']),
                        'script',
                        f"type={script['type']}, async={script['async']}, defer={script['defer']}"
                    ])
                
                # Write stylesheet dependencies
                for link in deps['stylesheets']:
                    writer.writerow([
                        html_file,
                        'css',
                        link['href'],
                        self.is_external_url(link['href']),
                        'link',
                        f"rel={link['rel']}, type={link['type']}, media={link['media']}"
                    ])
        
        print(f"Generated {output_file}")
    
    def print_summary(self):
        """Print summary of dependency analysis."""
        print(f"\n{'='*60}")
        print("DEPENDENCY MAPPING SUMMARY")
        print(f"{'='*60}")
        print(f"HTML files scanned: {len(self.html_files)}")
        print(f"Total JavaScript dependencies: {len(self.js_dependencies)}")
        print(f"Total CSS dependencies: {len(self.css_dependencies)}")
        print(f"External dependencies: {len(self.external_dependencies)}")
        print(f"Local dependencies: {len(self.local_dependencies)}")
        
        print(f"\nTop 10 Most Common External Dependencies:")
        external_counts = {}
        for html_file, deps in self.dependency_map.items():
            for script in deps['scripts']:
                if self.is_external_url(script['src']):
                    external_counts[script['src']] = external_counts.get(script['src'], 0) + 1
            for link in deps['stylesheets']:
                if self.is_external_url(link['href']):
                    external_counts[link['href']] = external_counts.get(link['href'], 0) + 1
        
        for dep, count in sorted(external_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {count}x: {dep}")
        
        print(f"\n{'='*60}")


def main():
    """Main function to run the Dependency Mapper Agent."""
    parser = argparse.ArgumentParser(description="Dependency Mapper Agent - Extract script and link dependencies")
    parser.add_argument("--dir", "-d", default=".", help="Directory to scan (default: current directory)")
    parser.add_argument("--simple", action="store_true", help="Generate simple text files only")
    parser.add_argument("--json", action="store_true", help="Generate JSON report")
    parser.add_argument("--csv", action="store_true", help="Generate CSV report")
    parser.add_argument("--all", action="store_true", help="Generate all report formats")
    parser.add_argument("--quiet", "-q", action="store_true", help="Suppress summary output")
    
    args = parser.parse_args()
    
    # Create mapper instance
    mapper = DependencyMapper(args.dir)
    
    # Find HTML files and scan dependencies
    mapper.find_html_files()
    mapper.scan_dependencies()
    
    # Generate reports based on arguments
    if args.all or args.simple or not any([args.json, args.csv]):
        mapper.generate_simple_lists()
    
    if args.all or args.json:
        mapper.generate_json_report()
    
    if args.all or args.csv:
        mapper.generate_csv_report()
    
    # Print summary unless quiet mode
    if not args.quiet:
        mapper.print_summary()


if __name__ == "__main__":
    main()
