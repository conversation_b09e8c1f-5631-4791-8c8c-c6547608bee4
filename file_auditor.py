#!/usr/bin/env python3
"""
File Auditor Agent
Task: List all .html, .css, .js files and record their sizes & locations.

This script provides comprehensive file auditing capabilities for web development projects.
"""

import os
import csv
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple
import argparse


class FileAuditor:
    """File Auditor Agent for scanning and analyzing web files."""
    
    def __init__(self, root_dir: str = "."):
        self.root_dir = Path(root_dir).resolve()
        self.target_extensions = ['.html', '.css', '.js']
        self.files_found = []
        
    def scan_files(self) -> List[Dict]:
        """Scan for all target files and collect their information."""
        print(f"Scanning directory: {self.root_dir}")
        print(f"Looking for files with extensions: {', '.join(self.target_extensions)}")
        
        files_data = []
        
        for ext in self.target_extensions:
            # Use glob to find all files with the target extension
            pattern = f"**/*{ext}"
            for file_path in self.root_dir.glob(pattern):
                if file_path.is_file():
                    try:
                        stat_info = file_path.stat()
                        relative_path = file_path.relative_to(self.root_dir)
                        
                        file_info = {
                            'path': str(relative_path),
                            'absolute_path': str(file_path),
                            'size_bytes': stat_info.st_size,
                            'size_kb': round(stat_info.st_size / 1024, 2),
                            'extension': ext,
                            'directory': str(relative_path.parent),
                            'filename': file_path.name,
                            'modified_time': datetime.fromtimestamp(stat_info.st_mtime).isoformat()
                        }
                        files_data.append(file_info)
                        
                    except (OSError, PermissionError) as e:
                        print(f"Warning: Could not access {file_path}: {e}")
        
        # Sort by extension, then by path
        files_data.sort(key=lambda x: (x['extension'], x['path']))
        self.files_found = files_data
        return files_data
    
    def generate_tsv_report(self, output_file: str = "audit.tsv"):
        """Generate TSV report as specified in the original requirement."""
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            for file_info in self.files_found:
                f.write(f"{file_info['path']}\t{file_info['size_bytes']} bytes\n")
        print(f"TSV report generated: {output_file}")
    
    def generate_csv_report(self, output_file: str = "audit_report.csv"):
        """Generate detailed CSV report."""
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            if self.files_found:
                fieldnames = self.files_found[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.files_found)
        print(f"CSV report generated: {output_file}")
    
    def generate_json_report(self, output_file: str = "audit_report.json"):
        """Generate JSON report for programmatic access."""
        report_data = {
            'scan_timestamp': datetime.now().isoformat(),
            'root_directory': str(self.root_dir),
            'total_files': len(self.files_found),
            'summary_by_extension': self._get_summary_by_extension(),
            'files': self.files_found
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        print(f"JSON report generated: {output_file}")
    
    def _get_summary_by_extension(self) -> Dict:
        """Get summary statistics by file extension."""
        summary = {}
        for ext in self.target_extensions:
            ext_files = [f for f in self.files_found if f['extension'] == ext]
            if ext_files:
                total_size = sum(f['size_bytes'] for f in ext_files)
                summary[ext] = {
                    'count': len(ext_files),
                    'total_size_bytes': total_size,
                    'total_size_kb': round(total_size / 1024, 2),
                    'average_size_bytes': round(total_size / len(ext_files), 2),
                    'largest_file': max(ext_files, key=lambda x: x['size_bytes']),
                    'smallest_file': min(ext_files, key=lambda x: x['size_bytes'])
                }
        return summary
    
    def print_summary(self):
        """Print a summary of the audit results."""
        if not self.files_found:
            print("No files found matching the criteria.")
            return
        
        print(f"\n{'='*60}")
        print("FILE AUDIT SUMMARY")
        print(f"{'='*60}")
        print(f"Total files found: {len(self.files_found)}")
        
        summary = self._get_summary_by_extension()
        for ext, stats in summary.items():
            print(f"\n{ext.upper()} FILES:")
            print(f"  Count: {stats['count']}")
            print(f"  Total size: {stats['total_size_kb']} KB ({stats['total_size_bytes']} bytes)")
            print(f"  Average size: {stats['average_size_bytes']} bytes")
            print(f"  Largest: {stats['largest_file']['path']} ({stats['largest_file']['size_bytes']} bytes)")
            print(f"  Smallest: {stats['smallest_file']['path']} ({stats['smallest_file']['size_bytes']} bytes)")
        
        print(f"\n{'='*60}")


def main():
    """Main function to run the File Auditor Agent."""
    parser = argparse.ArgumentParser(description="File Auditor Agent - Scan for .html, .css, .js files")
    parser.add_argument("--dir", "-d", default=".", help="Directory to scan (default: current directory)")
    parser.add_argument("--tsv", action="store_true", help="Generate TSV report")
    parser.add_argument("--csv", action="store_true", help="Generate CSV report")
    parser.add_argument("--json", action="store_true", help="Generate JSON report")
    parser.add_argument("--all", action="store_true", help="Generate all report formats")
    parser.add_argument("--quiet", "-q", action="store_true", help="Suppress summary output")
    
    args = parser.parse_args()
    
    # Create auditor instance
    auditor = FileAuditor(args.dir)
    
    # Scan files
    files_data = auditor.scan_files()
    
    # Generate reports based on arguments
    if args.all or args.tsv:
        auditor.generate_tsv_report()
    
    if args.all or args.csv:
        auditor.generate_csv_report()
    
    if args.all or args.json:
        auditor.generate_json_report()
    
    # If no specific format requested, generate all
    if not any([args.tsv, args.csv, args.json, args.all]):
        auditor.generate_tsv_report()
        auditor.generate_csv_report()
        auditor.generate_json_report()
    
    # Print summary unless quiet mode
    if not args.quiet:
        auditor.print_summary()


if __name__ == "__main__":
    main()
