#!/usr/bin/env python3
"""
Batch parse sizes from audit.tsv and create audit_report.csv
This implements the Python snippet mentioned in the requirements.
"""

import os
import csv

def parse_audit_tsv():
    """Parse the audit.tsv file and create a clean CSV report."""
    
    if not os.path.exists('audit.tsv'):
        print("Error: audit.tsv not found. Please run the audit script first.")
        return
    
    try:
        with open('audit.tsv', 'r', encoding='utf-8') as f, \
             open('audit_report.csv', 'w', newline='', encoding='utf-8') as w:
            
            reader = csv.reader(f, delimiter='\t')
            writer = csv.writer(w)
            
            # Write header
            writer.writerow(['path', 'size'])
            
            # Process each line
            for row in reader:
                if len(row) >= 2:
                    path = row[0]
                    size_str = row[1]
                    
                    # Extract numeric size from "X bytes" format
                    try:
                        size = int(size_str.split()[0])
                        writer.writerow([path, size])
                    except (ValueError, IndexError):
                        print(f"Warning: Could not parse size for {path}: {size_str}")
        
        print("✓ audit_report.csv created successfully")
        
        # Display some statistics
        with open('audit_report.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            files = list(reader)
            
        if files:
            total_size = sum(int(file['size']) for file in files)
            avg_size = total_size / len(files)
            
            print(f"Total files processed: {len(files)}")
            print(f"Total size: {total_size:,} bytes ({total_size/1024:.2f} KB)")
            print(f"Average file size: {avg_size:.2f} bytes")
            
            # Find largest and smallest files
            largest = max(files, key=lambda x: int(x['size']))
            smallest = min(files, key=lambda x: int(x['size']))
            
            print(f"Largest file: {largest['path']} ({int(largest['size']):,} bytes)")
            print(f"Smallest file: {smallest['path']} ({int(smallest['size']):,} bytes)")
    
    except Exception as e:
        print(f"Error processing files: {e}")

if __name__ == "__main__":
    parse_audit_tsv()
